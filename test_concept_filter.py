#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试概念和行业过滤功能
"""

from concept_sector_filter import filter_meaningful_concepts_and_sectors

def test_concept_filter():
    """测试概念过滤功能"""
    print("=== 测试概念过滤功能 ===")
    
    # 测试概念列表（包含需要过滤的概念）
    test_concepts = [
        '人工智能',      # 有意义的概念，应该保留
        '预亏预减',      # 无意义的概念，应该被过滤
        '新能源汽车',    # 有意义的概念，应该保留
        '融资融券',      # 无意义的概念，应该被过滤
        '芯片概念',      # 有意义的概念，应该保留
        '昨日涨停',      # 无意义的概念，应该被过滤
        '医疗器械',      # 有意义的概念，应该保留
        '预盈预增',      # 无意义的概念，应该被过滤
    ]
    
    print("原始概念列表:")
    for i, concept in enumerate(test_concepts, 1):
        print(f"  {i}. {concept}")
    
    # 应用过滤
    filtered_concepts = filter_meaningful_concepts_and_sectors(test_concepts)
    
    print("\n过滤后概念列表:")
    for i, concept in enumerate(filtered_concepts, 1):
        print(f"  {i}. {concept}")
    
    print(f"\n过滤前数量: {len(test_concepts)}")
    print(f"过滤后数量: {len(filtered_concepts)}")
    print(f"过滤掉数量: {len(test_concepts) - len(filtered_concepts)}")
    
    # 验证预期结果
    expected_concepts = ['人工智能', '新能源汽车', '芯片概念', '医疗器械']
    if set(filtered_concepts) == set(expected_concepts):
        print("✅ 概念过滤测试通过！")
    else:
        print("❌ 概念过滤测试失败！")
        print(f"期望结果: {expected_concepts}")
        print(f"实际结果: {filtered_concepts}")

def test_industry_filter():
    """测试行业过滤功能"""
    print("\n=== 测试行业过滤功能 ===")
    
    # 测试行业列表（包含需要过滤的行业）
    test_industries = [
        '软件开发',      # 有意义的行业，应该保留
        '沪股通',        # 无意义的行业，应该被过滤
        '医药制造',      # 有意义的行业，应该保留
        '深股通',        # 无意义的行业，应该被过滤
        '电子制造',      # 有意义的行业，应该保留
        'MSCI',          # 无意义的行业，应该被过滤
        '汽车制造',      # 有意义的行业，应该保留
    ]
    
    print("原始行业列表:")
    for i, industry in enumerate(test_industries, 1):
        print(f"  {i}. {industry}")
    
    # 应用过滤
    filtered_industries = filter_meaningful_concepts_and_sectors(test_industries)
    
    print("\n过滤后行业列表:")
    for i, industry in enumerate(filtered_industries, 1):
        print(f"  {i}. {industry}")
    
    print(f"\n过滤前数量: {len(test_industries)}")
    print(f"过滤后数量: {len(filtered_industries)}")
    print(f"过滤掉数量: {len(test_industries) - len(filtered_industries)}")
    
    # 验证预期结果
    expected_industries = ['软件开发', '医药制造', '电子制造', '汽车制造']
    if set(filtered_industries) == set(expected_industries):
        print("✅ 行业过滤测试通过！")
    else:
        print("❌ 行业过滤测试失败！")
        print(f"期望结果: {expected_industries}")
        print(f"实际结果: {filtered_industries}")

def test_dict_filter():
    """测试字典过滤功能"""
    print("\n=== 测试字典过滤功能 ===")
    
    # 测试概念字典（模拟聚气分析结果）
    test_concept_dict = {
        '人工智能': ['股票A', '股票B', '股票C'],
        '预亏预减': ['股票D', '股票E'],
        '新能源汽车': ['股票F', '股票G'],
        '融资融券': ['股票H'],
        '芯片概念': ['股票I', '股票J', '股票K', '股票L'],
    }
    
    print("原始概念字典:")
    for concept, stocks in test_concept_dict.items():
        print(f"  {concept}: {len(stocks)}只股票 ({', '.join(stocks)})")
    
    # 应用过滤
    filtered_dict = filter_meaningful_concepts_and_sectors(test_concept_dict)
    
    print("\n过滤后概念字典:")
    for concept, stocks in filtered_dict.items():
        print(f"  {concept}: {len(stocks)}只股票 ({', '.join(stocks)})")
    
    print(f"\n过滤前概念数量: {len(test_concept_dict)}")
    print(f"过滤后概念数量: {len(filtered_dict)}")
    print(f"过滤掉概念数量: {len(test_concept_dict) - len(filtered_dict)}")
    
    # 验证预期结果
    expected_concepts = {'人工智能', '新能源汽车', '芯片概念'}
    if set(filtered_dict.keys()) == expected_concepts:
        print("✅ 字典过滤测试通过！")
    else:
        print("❌ 字典过滤测试失败！")
        print(f"期望概念: {expected_concepts}")
        print(f"实际概念: {set(filtered_dict.keys())}")

if __name__ == "__main__":
    print("开始测试概念和行业过滤功能...\n")
    
    try:
        test_concept_filter()
        test_industry_filter()
        test_dict_filter()
        
        print("\n" + "="*50)
        print("🎉 所有测试完成！")
        print("✅ trading_monitor.py 已成功集成概念和行业过滤功能")
        print("📝 现在所有显示概念、行业的地方都会自动过滤掉无意义的项目")
        print("🚫 被过滤的概念包括：预亏预减、预盈预增、融资融券、昨日涨停等")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
