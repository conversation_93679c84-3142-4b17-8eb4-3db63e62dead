#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易监控模块
功能：
1. 判断是否交易日
2. 判断交易时段
3. 获取涨停股票数据
4. 下载概念行业数据
5. 提供可调用的接口

作者：基于test_limit_up.py重构
日期：2025-01-21
"""

import os
import time
import datetime
import json
from typing import List, Dict, Optional, Tuple
from xtquant import xtdata
import pandas as pd
from tqdm import tqdm

# ==================== 昨日涨停表现复盘增强功能 ====================

def analyze_promotion_result(stock_code: str, yesterday_boards: int, today_limit_up_codes: Dict, today_change_pct: float) -> str:
    """
    分析股票的晋级结果

    Args:
        stock_code: 股票代码
        yesterday_boards: 昨日连板数
        today_limit_up_codes: 今日涨停股票字典
        today_change_pct: 今日涨跌幅

    Returns:
        str: 晋级结果描述
    """
    try:
        # 检查是否今日涨停
        if stock_code in today_limit_up_codes:
            today_stock = today_limit_up_codes[stock_code]
            # 处理不同的数据结构
            if isinstance(today_stock, dict):
                today_boards_raw = today_stock.get('连板数', 1)
                # 确保连板数是整数
                if isinstance(today_boards_raw, str):
                    today_boards = int(today_boards_raw.replace('连板', '').replace('板', ''))
                else:
                    today_boards = int(today_boards_raw)
            else:
                today_boards = 1  # 默认首板

            # 确保yesterday_boards也是整数
            yesterday_boards = int(yesterday_boards)

            if today_boards == yesterday_boards + 1:
                return f"成功 ({today_boards}板)"
            elif today_boards > yesterday_boards + 1:
                return f"成功 ({today_boards}板)"
            else:
                return f"重新起板 ({today_boards}板)"
        else:
            # 未涨停，根据涨跌幅判断
            if today_change_pct >= 7.0:
                return "冲高回落"
            elif today_change_pct >= 3.0:
                return "高开回落"
            elif today_change_pct >= 0:
                return "震荡整理"
            elif today_change_pct >= -3.0:
                return "小幅回调"
            elif today_change_pct >= -7.0:
                return "大幅回调"
            else:
                return "核按钮"
    except Exception as e:
        # 调试信息
        print(f"[DEBUG] analyze_promotion_result error for {stock_code}: {e}")
        # 根据涨跌幅判断，不返回"数据异常"
        if today_change_pct >= 7.0:
            return "冲高回落"
        elif today_change_pct >= 3.0:
            return "高开回落"
        elif today_change_pct >= 0:
            return "震荡整理"
        elif today_change_pct >= -3.0:
            return "小幅回调"
        elif today_change_pct >= -7.0:
            return "大幅回调"
        else:
            return "核按钮"


def calculate_market_position(yesterday_boards: int, promotion_result: str) -> str:
    """
    计算市场地位

    Args:
        yesterday_boards: 昨日连板数
        promotion_result: 晋级结果

    Returns:
        str: 市场地位
    """
    if yesterday_boards >= 7:
        return "总龙头"
    elif yesterday_boards >= 5:
        return "最高标"
    elif yesterday_boards >= 3:
        return "高标"
    elif yesterday_boards >= 2:
        return "中标"
    elif "成功" in promotion_result:
        return "低标"
    else:
        return "淘汰"


def get_auction_performance(stock_code: str, date_str: str, pre_close: float) -> str:
    """
    获取集合竞价表现

    Args:
        stock_code: 股票代码
        date_str: 日期字符串
        pre_close: 前收盘价

    Returns:
        str: 竞价表现描述，格式："金额 / 涨幅%"
    """
    try:
        # 先下载历史数据确保有数据
        xtdata.download_history_data(stock_code, period='1m', start_time=date_str, incrementally=True)

        # 获取当日1分钟K线数据，包含集合竞价
        data = xtdata.get_local_data(
            field_list=["time", "open", "high", "low", "close", "volume", "amount"],
            stock_list=[stock_code],
            period="1m",
            start_time=date_str,
            end_time=date_str
        )

        if data and stock_code in data:
            df = data[stock_code]
            if not df.empty:
                # 取第一根K线的开盘价作为集合竞价价格
                first_bar = df.iloc[0]
                auction_price = first_bar['open']
                auction_volume = first_bar['volume']
                auction_amount = first_bar['amount']

                if auction_price > 0 and pre_close > 0:
                    auction_change_pct = ((auction_price - pre_close) / pre_close) * 100
                    auction_amount_wan = auction_amount / 10000  # 转换为万元

                    return f"{auction_amount_wan:.0f}万 / {auction_change_pct:+.1f}%"

        return "-- / --"
    except Exception as e:
        return "-- / 数据异常"


def get_intraday_strength(stock_code: str, date_str: str) -> str:
    """
    获取分时强度

    Args:
        stock_code: 股票代码
        date_str: 日期字符串

    Returns:
        str: 分时强度描述
    """
    try:
        # 先下载历史数据确保有数据
        xtdata.download_history_data(stock_code, period='1m', start_time=date_str, incrementally=True)

        # 获取当日1分钟K线数据
        data = xtdata.get_local_data(
            field_list=["time", "open", "high", "low", "close", "volume"],
            stock_list=[stock_code],
            period="1m",
            start_time=date_str,
            end_time=date_str
        )

        if data and stock_code in data:
            df = data[stock_code]
            if not df.empty:
                # 获取开盘价和收盘价
                open_price = df.iloc[0]['open']
                close_price = df.iloc[-1]['close']

                if open_price > 0:
                    # 计算涨跌幅
                    change_pct = ((close_price - open_price) / open_price) * 100

                    # 判断分时强度
                    if change_pct >= 9.5:
                        return "强势涨停"
                    elif change_pct >= 5.0:
                        return "大涨"
                    elif change_pct >= 2.0:
                        return "中涨"
                    elif change_pct >= 0:
                        return "小涨"
                    elif change_pct >= -2.0:
                        return "小跌"
                    elif change_pct >= -5.0:
                        return "中跌"
                    else:
                        return "大跌"

        return "数据异常"
    except Exception:
        return "数据异常"


def get_seal_strength(stock_code: str, date_str: str, today_limit_up_codes: Dict) -> str:
    """
    获取封单强度

    Args:
        stock_code: 股票代码
        date_str: 日期字符串
        today_limit_up_codes: 今日涨停股票字典

    Returns:
        str: 封单强度描述，格式："金额 / 强度"
    """
    try:
        # 检查是否今日涨停
        if stock_code not in today_limit_up_codes:
            return "-- / 未封板"

        # 获取tick数据来计算封单
        tick_data = xtdata.get_full_tick([stock_code])

        if tick_data and stock_code in tick_data:
            tick_info = tick_data[stock_code]

            # 获取买一档数据（涨停时的封单）
            bid_price = tick_info.get('bidPrice', [0])[0] if tick_info.get('bidPrice') else 0
            bid_vol = tick_info.get('bidVol', [0])[0] if tick_info.get('bidVol') else 0

            if bid_price > 0 and bid_vol > 0:
                seal_amount = bid_price * bid_vol / 10000  # 转换为万元

                # 简化的强度评级（不依赖流通市值）
                if seal_amount > 10000:  # 1亿以上
                    strength = "极强"
                elif seal_amount > 5000:  # 5000万以上
                    strength = "很强"
                elif seal_amount > 1000:  # 1000万以上
                    strength = "强"
                elif seal_amount > 500:   # 500万以上
                    strength = "中等"
                else:
                    strength = "一般"

                return f"{seal_amount:.1f}万 / {strength}"

        return "-- / 数据异常"
    except Exception:
        return "-- / 数据异常"


def calculate_closing_premium(close_price: float, pre_close: float) -> str:
    """
    计算收盘溢价

    Args:
        close_price: 收盘价
        pre_close: 前收盘价

    Returns:
        str: 收盘溢价百分比
    """
    try:
        if close_price > 0 and pre_close > 0:
            premium = ((close_price - pre_close) / pre_close) * 100
            return f"{premium:+.1f}%"
        return "0.0%"
    except Exception:
        return "0.0%"


def generate_market_interpretation(stock_code: str, stock_name: str, market_position: str,
                                 promotion_result: str, auction_performance: str,
                                 intraday_strength: str, seal_strength: str) -> str:
    """
    生成盘口语言解读

    Args:
        stock_code: 股票代码
        stock_name: 股票名称
        market_position: 市场地位
        promotion_result: 晋级结果
        auction_performance: 竞价表现
        intraday_strength: 分时强度
        seal_strength: 封单强度

    Returns:
        str: 盘口语言解读
    """
    try:
        interpretation_parts = []

        # 基于市场地位的描述
        if market_position == "总龙头":
            interpretation_parts.append("市场总龙头")
        elif market_position in ["最高标", "高标"]:
            interpretation_parts.append("板块卡位龙头")
        elif market_position == "中标":
            interpretation_parts.append("板块二线龙头")

        # 基于晋级结果的描述
        if "成功" in promotion_result:
            interpretation_parts.append("封板坚决")
        elif "回调" in promotion_result:
            interpretation_parts.append("资金大幅撤离")
        elif "冲高回落" in promotion_result:
            interpretation_parts.append("有资金畏高兑现，但承接尚可")
        elif "高开回落" in promotion_result:
            interpretation_parts.append("市场表现平淡")

        # 基于竞价表现的描述
        if "+" in auction_performance and not auction_performance.startswith("--"):
            interpretation_parts.append("竞价强势")

        # 基于分时强度的描述
        if intraday_strength == "强势涨停":
            interpretation_parts.append("强化题材")

        # 组合描述
        if interpretation_parts:
            return "，".join(interpretation_parts) + "。"
        else:
            return "市场表现一般。"

    except Exception:
        return "数据分析异常。"


# 配置参数
DEBUG_PRINT = False  # 是否打印调试信息
TARGET_STOCK_PREFIXES = ('60', '00', '002', '300', '301', '688')  # 目标股票前缀
DATA_SAVE_DIR = "data"  # 数据保存目录

# 全局缓存
TARGET_STOCKS_CACHE = {}  # 股票基础信息缓存
STOCK_INDUSTRY_CACHE = {}  # 行业信息缓存
STOCK_CONCEPT_CACHE = {}   # 概念信息缓存

# 主线识别相关缓存
MAIN_THEME_CACHE = {}  # 主线识别结果缓存


class TradingMonitorConfig:
    """交易监控配置类"""

    def __init__(self):
        self.debug_print = DEBUG_PRINT
        self.target_prefixes = TARGET_STOCK_PREFIXES
        self.data_dir = DATA_SAVE_DIR
        self.monitor_interval = 30  # 监控间隔（秒）
        self.max_display_stocks = 20  # 最大显示股票数量
        self.enable_sound_alert = False  # 是否启用声音提醒
        self.alert_threshold = 50  # 涨停数量提醒阈值

    def load_from_file(self, config_file: str = "trading_config.json"):
        """从文件加载配置"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                for key, value in config_data.items():
                    if hasattr(self, key):
                        setattr(self, key, value)

                print(f">>> 配置已从 {config_file} 加载")
            else:
                print(f">>> 配置文件 {config_file} 不存在，使用默认配置")
        except Exception as e:
            print(f">>> 加载配置失败: {e}")

    def save_to_file(self, config_file: str = "trading_config.json"):
        """保存配置到文件"""
        try:
            config_data = {
                'debug_print': self.debug_print,
                'target_prefixes': self.target_prefixes,
                'data_dir': self.data_dir,
                'monitor_interval': self.monitor_interval,
                'max_display_stocks': self.max_display_stocks,
                'enable_sound_alert': self.enable_sound_alert,
                'alert_threshold': self.alert_threshold
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            print(f">>> 配置已保存到 {config_file}")
        except Exception as e:
            print(f">>> 保存配置失败: {e}")


# 全局配置实例
config = TradingMonitorConfig()


def ensure_data_dir(date_str: str = None) -> str:
    """确保数据目录存在"""
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')
    
    daily_dir = os.path.join(DATA_SAVE_DIR, date_str)
    if not os.path.exists(daily_dir):
        os.makedirs(daily_dir)
    return daily_dir


def get_last_trading_day(base_date: str = None) -> str:
    """
    获取上一个交易日

    Args:
        base_date: 基准日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        str: 上一个交易日的日期字符串
    """
    if base_date is None:
        current_date = datetime.datetime.now()
    else:
        current_date = datetime.datetime.strptime(base_date, '%Y%m%d')

    # 向前查找交易日，最多查找10天
    for i in range(1, 11):
        check_date = current_date - datetime.timedelta(days=i)
        check_date_str = check_date.strftime('%Y%m%d')

        if is_trading_day(check_date_str):
            return check_date_str

    # 如果找不到，返回昨天
    yesterday = current_date - datetime.timedelta(days=1)
    return yesterday.strftime('%Y%m%d')


def is_trading_day(date_str: str = None) -> bool:
    """
    判断是否为交易日
    
    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天
        
    Returns:
        bool: True表示是交易日，False表示不是
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取交易日历
        try:
            trading_calendar = xtdata.get_trading_dates(market='SH', start_time=date_str, end_time=date_str)
            if trading_calendar:
                # 将时间戳转换为日期字符串进行比较
                for timestamp in trading_calendar:
                    if timestamp > 1000000000000:  # 毫秒时间戳
                        dt = datetime.datetime.fromtimestamp(timestamp / 1000)
                    else:  # 秒时间戳
                        dt = datetime.datetime.fromtimestamp(timestamp)
                    if dt.strftime('%Y%m%d') == date_str:
                        return True
                return False
            else:
                # 如果获取失败，使用简单的工作日判断
                date_obj = datetime.datetime.strptime(date_str, '%Y%m%d')
                return date_obj.weekday() < 5  # 周一到周五
        except Exception as e:
            if DEBUG_PRINT:
                print(f"[DEBUG] 获取交易日历失败: {e}")
            # 如果获取失败，使用简单的工作日判断
            date_obj = datetime.datetime.strptime(date_str, '%Y%m%d')
            return date_obj.weekday() < 5  # 周一到周五
            
    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 判断交易日失败: {e}")
        return False


def is_trading_time(stock_code: str = '600000.SH') -> bool:
    """
    判断当前是否为交易时间
    
    Args:
        stock_code: 股票代码，用于获取交易时段
        
    Returns:
        bool: True表示是交易时间，False表示不是
    """
    try:
        # 获取交易时段
        trading_times = xtdata.get_trading_time(stock_code)
        if not trading_times:
            # 如果获取失败，使用默认交易时间
            return is_default_trading_time()

        now = datetime.datetime.now()
        current_seconds = now.hour * 3600 + now.minute * 60 + now.second

        for time_segment in trading_times:
            start_time, end_time, trade_type = time_segment
            # 只考虑连续交易时段 (trade_type == 3)
            if trade_type == 3 and start_time <= current_seconds <= end_time:
                return True

        return False
    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 获取交易时段失败: {e}")
        # 如果获取失败，使用默认交易时间判断
        return is_default_trading_time()


def is_default_trading_time() -> bool:
    """默认交易时间判断（9:30-11:30, 13:00-15:00）"""
    now = datetime.datetime.now()
    current_time = now.strftime('%H%M')

    # 交易时间：9:30-11:30, 13:00-15:00
    morning_start, morning_end = '0930', '1130'
    afternoon_start, afternoon_end = '1300', '1500'

    return (morning_start <= current_time <= morning_end) or (afternoon_start <= current_time <= afternoon_end)


def is_after_trading_hours() -> bool:
    """判断是否为收盘后时间（15:00之后）"""
    now = datetime.datetime.now()
    current_time = now.strftime('%H%M')
    return current_time >= '1500'


def load_target_stocks() -> Dict[str, Dict]:
    """加载目标股票列表（参考test_limit_up.py的实现）"""
    global TARGET_STOCKS_CACHE

    if TARGET_STOCKS_CACHE:
        return TARGET_STOCKS_CACHE

    try:
        print(">>> 正在加载股票基础信息...")

        # 步骤1: 获取所有股票，包含北交所
        print(f">>> 步骤1/3: 筛选代码前缀为 {TARGET_STOCK_PREFIXES} 的股票...")

        # 尝试获取更全面的股票列表
        try:
            all_stocks = xtdata.get_stock_list_in_sector('沪深A股')
            print(f">>> 获取到沪深A股 {len(all_stocks)} 只")

            # 尝试获取北交所股票
            try:
                bse_stocks = xtdata.get_stock_list_in_sector('北交所')
                if bse_stocks:
                    all_stocks.extend(bse_stocks)
                    print(f">>> 获取到北交所股票 {len(bse_stocks)} 只")
            except:
                print(">>> 无法获取北交所股票列表，跳过")

        except:
            # 备用方案：获取所有股票
            all_stocks = xtdata.get_stock_list_in_sector('全部A股')
            if not all_stocks:
                all_stocks = xtdata.get_stock_list_in_sector('A股')

        if not all_stocks:
            print(">>> 无法获取股票列表")
            return {}

        print(f">>> 总共获取到 {len(all_stocks)} 只股票")

        # 根据前缀筛选目标股票
        target_stock_list = []
        for code in all_stocks:
            stock_code = code.split('.')[0]
            # 检查是否匹配目标前缀
            for prefix in TARGET_STOCK_PREFIXES:
                if prefix == '8':  # 北交所股票特殊处理
                    if stock_code.startswith('8'):
                        target_stock_list.append(code)
                        break
                else:  # 其他股票按原逻辑
                    if stock_code.startswith(prefix):
                        target_stock_list.append(code)
                        break

        print(f">>> 筛选出 {len(target_stock_list)} 只目标股票")

        # 步骤2: 过滤ST股并缓存详情
        print(">>> 步骤2/3: 过滤ST股并缓存股票详情...")
        for code in tqdm(target_stock_list, desc="加载股票详情"):
            try:
                detail = xtdata.get_instrument_detail(code)
                if not detail:
                    continue

                # 加上类型判断，确保是股票
                try:
                    instrument_type = xtdata.get_instrument_type(code)
                    is_stock = instrument_type.get('stock', False) if instrument_type else True
                except:
                    is_stock = True  # 如果获取类型失败，默认认为是股票

                instrument_name = detail.get('InstrumentName', '')

                # 过滤ST股票、退市股票和其他特殊标记股票
                if (is_stock and
                    'ST' not in instrument_name and
                    '*' not in instrument_name and
                    '退市' not in instrument_name):
                    TARGET_STOCKS_CACHE[code] = detail

            except Exception as e:
                if DEBUG_PRINT:
                    print(f"[DEBUG] 处理 {code} 失败: {e}")
                continue

        print(f">>> 步骤3/3: 成功缓存 {len(TARGET_STOCKS_CACHE)} 只目标股票")
        return TARGET_STOCKS_CACHE

    except Exception as e:
        print(f">>> 加载股票基础信息失败: {e}")
        return {}


def download_sector_data_if_needed() -> bool:
    """检查并下载概念行业数据（参考test_limit_up.py的完善实现）"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    try:
        print(">>> 开始加载板块分类信息...")

        # 检查本地板块数据
        existing_sectors = xtdata.get_sector_list()
        sector_count = len(existing_sectors)
        print(f">>> [发现] 本地已有 {sector_count} 个板块")

        if sector_count >= 50:
            print(">>> [使用] 本地板块数据充足，开始处理...")
            process_sector_data_standard(existing_sectors)
            return True
        else:
            print(">>> [下载] 本地板块数据不足，开始下载...")

        # 下载板块数据
        print(">>> [下载] 开始下载板块数据...")
        xtdata.download_sector_data()

        # 重新获取板块列表
        all_sectors = xtdata.get_sector_list()
        print(f">>> [完成] 下载完成，现在有 {len(all_sectors)} 个板块")
        process_sector_data_standard(all_sectors)

        return True

    except Exception as e:
        print(f">>> [失败] 下载失败: {e}")
        use_fallback_strategy_enhanced()
        return False


def process_sector_data_standard(all_sectors):
    """使用XtQuant标准板块名称处理数据（参考test_limit_up.py）"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    print(">>> [处理] 开始处理标准板块数据...")

    # XtQuant标准行业板块名称
    standard_industry_sectors = [
        '申万一级行业', '申万二级行业', '申万三级行业',
        '证监会行业', '证监会行业板块指数',
        'Wind行业', 'SW行业', '行业板块',
        '银行', '保险', '证券', '房地产', '建筑装饰', '钢铁', '有色金属',
        '煤炭', '石油石化', '化工', '建筑材料', '机械设备', '电气设备',
        '国防军工', '汽车', '家用电器', '纺织服装', '轻工制造', '医药生物',
        '商业贸易', '休闲服务', '农林牧渔', '食品饮料', '计算机', '电子',
        '通信', '传媒', '公用事业', '交通运输', '非银金融', '综合'
    ]

    # XtQuant标准概念板块名称
    standard_concept_sectors = [
        '概念板块', '主题概念', '热点概念', 'TGN概念',
        '新能源', '人工智能', '5G', '新基建', '军工', '芯片', '半导体',
        '新材料', '生物医药', '创新药', '疫苗', '医疗器械', '互联网',
        '物联网', '大数据', '云计算', '区块链', '新零售', '消费电子',
        '智能制造', '工业4.0', '新能源汽车', '锂电池', '光伏', '风电',
        '环保', '节能', '数字经济', '元宇宙', '碳中和', '氢能源',
        '储能', '充电桩', '智能驾驶', '无人驾驶'
    ]

    # 精确匹配标准行业板块
    industry_sectors = []
    for sector in all_sectors:
        # 行业板块匹配
        if any(keyword in sector for keyword in standard_industry_sectors):
            industry_sectors.append(sector)

    # 精确匹配标准概念板块
    concept_sectors = []
    for sector in all_sectors:
        # 概念板块匹配
        if any(keyword in sector for keyword in standard_concept_sectors):
            concept_sectors.append(sector)
        # 或者包含'TGN'前缀的概念板块
        elif sector.startswith('TGN'):
            concept_sectors.append(sector)

    print(f">>> [匹配] 标准行业板块: {len(industry_sectors)} 个")
    print(f">>> [匹配] 标准概念板块: {len(concept_sectors)} 个")

    # 显示前几个作为示例
    if industry_sectors:
        print(f">>> [示例] 行业板块: {industry_sectors[:3]}")
    if concept_sectors:
        print(f">>> [示例] 概念板块: {concept_sectors[:3]}")

    # 建立行业映射
    print(">>> [映射] 开始建立股票-行业映射...")
    build_sector_mapping_optimized(industry_sectors, STOCK_INDUSTRY_CACHE, "行业", 2)

    # 建立概念映射
    print(">>> [映射] 开始建立股票-概念映射...")
    build_sector_mapping_optimized(concept_sectors, STOCK_CONCEPT_CACHE, "概念", 3)

    # 补充未分类股票
    supplement_uncategorized_stocks()

    # 最终统计
    industry_coverage = len(STOCK_INDUSTRY_CACHE) / len(TARGET_STOCKS_CACHE) if TARGET_STOCKS_CACHE else 0
    concept_coverage = len(STOCK_CONCEPT_CACHE) / len(TARGET_STOCKS_CACHE) if TARGET_STOCKS_CACHE else 0
    print(f">>> [完成] 行业覆盖率: {industry_coverage:.1%} ({len(STOCK_INDUSTRY_CACHE)}只)")
    print(f">>> [完成] 概念覆盖率: {concept_coverage:.1%} ({len(STOCK_CONCEPT_CACHE)}只)")

    # 新增：融合akshare数据
    merge_akshare_data_into_cache()


def build_sector_mapping_optimized(sectors, cache_dict, sector_type, max_per_stock):
    """优化的板块映射建立（提高成功率）"""
    if not sectors:
        print(f">>> [警告] 没有可用的{sector_type}板块数据")
        return

    success_count = 0
    error_count = 0

    # 优先处理最重要的板块
    priority_sectors = []
    normal_sectors = []

    for sector in sectors:
        # 优先级判断 - 标准板块优先
        if any(priority_keyword in sector for priority_keyword in [
            '申万一级', '申万二级', '证监会行业', 'TGN',
            '新能源', '人工智能', '5G', '军工', '医药', '芯片'
        ]):
            priority_sectors.append(sector)
        else:
            normal_sectors.append(sector)

    # 处理高优先级板块
    sorted_sectors = priority_sectors + normal_sectors
    max_process = min(30, len(sorted_sectors))  # 增加处理数量
    sectors_to_process = sorted_sectors[:max_process]

    print(f">>> [处理] 将处理 {len(sectors_to_process)} 个{sector_type}板块")

    for i, sector in enumerate(sectors_to_process):
        try:
            print(f">>> [进度] {sector_type} {i + 1}/{len(sectors_to_process)}: {sector[:30]}...", end="")

            stocks_in_sector = xtdata.get_stock_list_in_sector(sector)
            stock_count = 0

            for stock_code in stocks_in_sector:
                if stock_code in TARGET_STOCKS_CACHE:
                    if stock_code not in cache_dict:
                        cache_dict[stock_code] = []

                    # 简化板块名称
                    simplified_name = simplify_sector_name_enhanced(sector)
                    if simplified_name and len(cache_dict[stock_code]) < max_per_stock:
                        cache_dict[stock_code].append(simplified_name)
                        stock_count += 1

            print(f" ✓ 成功: {stock_count} 只股票")
            success_count += 1

        except Exception as e:
            error_count += 1
            print(f" ✗ 失败: {e}")

    print(f">>> [完成] {sector_type}映射处理完成！成功: {success_count}, 失败: {error_count}")


def simplify_sector_name_enhanced(sector_name):
    """增强的板块名称简化（处理THY1等前缀）"""
    import re
    simplified = sector_name

    # 移除常见板块类型前缀
    prefixes_to_remove = [
        '申万一级', '申万二级', '申万三级', '证监会行业', 'Wind行业',
        '行业板块', '概念板块', '主题概念', '热点概念', 'TGN'
    ]

    for prefix in prefixes_to_remove:
        if simplified.startswith(prefix):
            simplified = simplified[len(prefix):].strip()

    # 使用正则表达式移除数据源特定的字母数字前缀，如 "THY1"
    simplified = re.sub(r'^[A-Z0-9]+\s*', '', simplified).strip()

    # 清理多余的标点符号
    simplified = simplified.strip('- _()（）')

    # 如果简化后名称为空或过短，则返回原始名称以保证有内容显示
    if len(simplified) <= 1:
        return sector_name

    return simplified


def merge_akshare_data_into_cache():
    """
    读取akshare数据文件，并将其融合进全局缓存
    心法：海纳百川，有容乃大
    """
    print(">>> [融合] 开始加载并融合 akshare 数据...")

    # 融合概念数据
    ak_concept_map_path = "akshare_data/ak_stock_concept_map.json"
    if os.path.exists(ak_concept_map_path):
        try:
            with open(ak_concept_map_path, 'r', encoding='utf-8') as f:
                ak_stock_concept_map = json.load(f)

            merged_concept_count = 0
            for stock_code, ak_concepts in ak_stock_concept_map.items():
                if stock_code in TARGET_STOCKS_CACHE:
                    # 获取xtquant已有的概念
                    xt_concepts = set(STOCK_CONCEPT_CACHE.get(stock_code, []))
                    # 合并并去重
                    xt_concepts.update(ak_concepts)
                    # 写回缓存
                    STOCK_CONCEPT_CACHE[stock_code] = sorted(list(xt_concepts))
                    merged_concept_count += 1

            print(f">>> [融合] 成功融合 {merged_concept_count} 只股票的概念数据")

        except Exception as e:
            print(f">>> [错误] 融合概念数据失败: {e}")
    else:
        print(">>> [警告] 未找到 akshare 概念数据文件，跳过概念融合")

    # 融合行业数据
    ak_industry_map_path = "akshare_data/ak_stock_industry_map.json"
    if os.path.exists(ak_industry_map_path):
        try:
            with open(ak_industry_map_path, 'r', encoding='utf-8') as f:
                ak_stock_industry_map = json.load(f)

            merged_industry_count = 0
            for stock_code, ak_industries in ak_stock_industry_map.items():
                if stock_code in TARGET_STOCKS_CACHE:
                    # 获取xtquant已有的行业
                    xt_industries = set(STOCK_INDUSTRY_CACHE.get(stock_code, []))
                    # 合并并去重
                    xt_industries.update(ak_industries)
                    # 写回缓存
                    STOCK_INDUSTRY_CACHE[stock_code] = sorted(list(xt_industries))
                    merged_industry_count += 1

            print(f">>> [融合] 成功融合 {merged_industry_count} 只股票的行业数据")

        except Exception as e:
            print(f">>> [错误] 融合行业数据失败: {e}")
    else:
        print(">>> [警告] 未找到 akshare 行业数据文件，跳过行业融合")

    # 更新覆盖率统计
    if TARGET_STOCKS_CACHE:
        industry_coverage = len(STOCK_INDUSTRY_CACHE) / len(TARGET_STOCKS_CACHE)
        concept_coverage = len(STOCK_CONCEPT_CACHE) / len(TARGET_STOCKS_CACHE)
        print(f">>> [融合后] 行业覆盖率: {industry_coverage:.1%} ({len(STOCK_INDUSTRY_CACHE)}只)")
        print(f">>> [融合后] 概念覆盖率: {concept_coverage:.1%} ({len(STOCK_CONCEPT_CACHE)}只)")


def supplement_uncategorized_stocks():
    """补充未分类的股票"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    # 为未分类的股票添加基础分类
    for stock_code in TARGET_STOCKS_CACHE:
        # 行业分类补充
        if stock_code not in STOCK_INDUSTRY_CACHE or not STOCK_INDUSTRY_CACHE[stock_code]:
            code_prefix = stock_code[:3]
            if code_prefix.startswith('60'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['沪市主板']
            elif code_prefix.startswith('00'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['深市主板']
            elif code_prefix == '002':
                STOCK_INDUSTRY_CACHE[stock_code] = ['深市中小板']
            elif code_prefix in ('300', '301'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['创业板']
            elif code_prefix.startswith('68'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['科创板']
            elif code_prefix.startswith('8'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['北交所']
            else:
                STOCK_INDUSTRY_CACHE[stock_code] = ['其他']

        # 概念分类补充 - 只添加有意义的概念
        if stock_code not in STOCK_CONCEPT_CACHE or not STOCK_CONCEPT_CACHE[stock_code]:
            # 不添加基础概念，留空等待真正的概念数据
            STOCK_CONCEPT_CACHE[stock_code] = []

    print(f">>> [补充] 确保所有股票都有行业分类")


def use_fallback_strategy_enhanced():
    """增强的备用策略"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    print(">>> [增强备用策略] 开始执行...")

    # 检查是否有任何可用的本地板块数据
    try:
        existing_sectors = xtdata.get_sector_list()
        if len(existing_sectors) > 5:  # 降低门槛
            print(f">>> [策略1] 使用可用的本地数据：{len(existing_sectors)} 个板块")
            process_sector_data_enhanced(existing_sectors)
            return
    except Exception as e:
        print(f">>> [策略1] 失败: {e}")

    # 直接使用智能分类策略
    print(">>> [策略2] 使用纯智能分类策略...")
    supplement_with_intelligent_classification()

    print(">>> [策略2] 智能分类完成，实现100%覆盖")


def process_sector_data_enhanced(all_sectors):
    """增强的板块数据处理（改进版）"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    print(">>> [分析] 开始分析和分类板块...")

    # 扩展的行业和概念关键词
    industry_keywords = [
        # 申万/证监会行业
        '申万一级', '申万二级', '申万三级', '证监会行业', 'Wind行业', 'SW行业',
        '行业板块', '行业指数', '行业分类',
        # 具体行业名称（大幅扩展）
        '银行', '保险', '证券', '房地产', '建筑', '钢铁', '有色', '煤炭',
        '石油', '化工', '石化', '建材', '机械', '电气', '国防', '汽车',
        '家电', '纺织', '轻工', '医药', '商贸', '餐饮', '农业', '林业',
        '食品', '饮料', '计算机', '电子', '通信', '传媒', '公用事业',
        '交通运输', '非银金融', '综合', '休闲服务', '商业贸易'
    ]

    concept_keywords = [
        # 概念主题
        '概念', '主题', '题材', 'TGN', '热点',
        # 热门概念（大幅扩展）
        '新能源', '人工智能', '5G', '新基建', '军工', '芯片', '半导体',
        '新材料', '生物医药', '创新药', '疫苗', '医疗器械', '互联网',
        '物联网', '大数据', '云计算', '区块链', '新零售', '消费电子',
        '智能制造', '工业4.0', '新能源汽车', '锂电池', '光伏', '风电',
        '环保', '节能', '数字经济', '元宇宙', '碳中和', '氢能源',
        '储能', '充电桩', '智能驾驶', '无人驾驶', 'VR', 'AR',
        '网络安全', '信息安全', '工业互联网', '边缘计算'
    ]

    # 改进的板块分类逻辑
    industry_sectors = []
    concept_sectors = []

    for sector in all_sectors:
        # 过滤掉非股票相关板块
        if any(keyword in sector for keyword in ['期货', '期权', 'ETF基金', '可转债', '债券']):
            continue

        # 行业板块匹配（扩大匹配范围）
        if any(keyword in sector for keyword in industry_keywords):
            industry_sectors.append(sector)
        # 概念板块匹配
        elif any(keyword in sector for keyword in concept_keywords):
            concept_sectors.append(sector)

    print(f">>> [分类] 行业板块: {len(industry_sectors)} 个")
    print(f">>> [分类] 概念板块: {len(concept_sectors)} 个")

    # 显示前几个作为示例
    if industry_sectors:
        print(f">>> [示例] 行业板块: {industry_sectors[:3]}")
    if concept_sectors:
        print(f">>> [示例] 概念板块: {concept_sectors[:3]}")

    # 建立行业映射
    print(">>> [映射] 开始建立股票-行业映射...")
    build_sector_mapping_optimized(industry_sectors, STOCK_INDUSTRY_CACHE, "行业", 2)

    # 建立概念映射
    print(">>> [映射] 开始建立股票-概念映射...")
    build_sector_mapping_optimized(concept_sectors, STOCK_CONCEPT_CACHE, "概念", 3)

    # 使用智能补充策略确保100%覆盖
    supplement_with_intelligent_classification()

    # 最终统计
    industry_coverage = len(STOCK_INDUSTRY_CACHE) / len(TARGET_STOCKS_CACHE) if TARGET_STOCKS_CACHE else 0
    concept_coverage = len(STOCK_CONCEPT_CACHE) / len(TARGET_STOCKS_CACHE) if TARGET_STOCKS_CACHE else 0
    print(f">>> [最终] 行业覆盖率: {industry_coverage:.1%} ({len(STOCK_INDUSTRY_CACHE)}只)")
    print(f">>> [最终] 概念覆盖率: {concept_coverage:.1%} ({len(STOCK_CONCEPT_CACHE)}只)")


def supplement_with_intelligent_classification():
    """智能补充分类，确保100%覆盖率"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    print(">>> [智能补充] 开始智能分类补充...")

    # 扩展的智能分类映射
    intelligent_industry_mapping = {
        # 更细致的前缀映射
        '600': '沪市主板-传统产业',
        '601': '沪市主板-金融业',  # 601多为银行
        '603': '沪市主板-制造业',  # 603多为制造业
        '605': '沪市主板-新兴产业',  # 605多为新兴行业
        '000': '深市主板-综合类',
        '001': '深市主板-创新类',
        '002': '深市中小板',
        '300': '创业板-高科技',
        '301': '创业板-新经济',
        '688': '科创板-硬科技',
        '689': '科创板-生物医药',
        '8': '北交所-专精特新'
    }

    # 只为真正没有概念的股票添加有意义的概念
    intelligent_concept_mapping = {
        '688': ['硬科技', '科技创新', '国产替代'],
        '689': ['生物医药', '医疗健康', '创新药'],
        '8': ['专精特新', '小巨人企业']
    }

    supplemented_industry = 0
    supplemented_concept = 0

    for stock_code in TARGET_STOCKS_CACHE:
        # 行业分类补充
        if stock_code not in STOCK_INDUSTRY_CACHE or not STOCK_INDUSTRY_CACHE[stock_code]:
            # 尝试不同长度的前缀匹配
            classified = False
            for prefix_len in [3, 1]:  # 先3位前缀，再1位前缀
                prefix = stock_code[:prefix_len]
                if prefix in intelligent_industry_mapping:
                    STOCK_INDUSTRY_CACHE[stock_code] = [intelligent_industry_mapping[prefix]]
                    supplemented_industry += 1
                    classified = True
                    break

            # 默认行业分类
            if not classified:
                if stock_code.endswith('.SH'):
                    STOCK_INDUSTRY_CACHE[stock_code] = ['沪市股票']
                else:
                    STOCK_INDUSTRY_CACHE[stock_code] = ['深市股票']
                supplemented_industry += 1

        # 概念分类补充 - 只为特定板块添加有意义的概念
        if stock_code not in STOCK_CONCEPT_CACHE or not STOCK_CONCEPT_CACHE[stock_code]:
            classified = False
            for prefix_len in [3, 1]:
                prefix = stock_code[:prefix_len]
                if prefix in intelligent_concept_mapping:
                    STOCK_CONCEPT_CACHE[stock_code] = intelligent_concept_mapping[prefix]
                    supplemented_concept += 1
                    classified = True
                    break

            # 对于其他股票，不添加无意义的概念，保持空列表
            if not classified:
                STOCK_CONCEPT_CACHE[stock_code] = []

    print(f">>> [智能补充完成] 行业: +{supplemented_industry}只, 概念: +{supplemented_concept}只")


def get_stock_industry_info(stock_code: str) -> List[str]:
    """获取股票行业信息（使用缓存，过滤无意义信息）"""
    global STOCK_INDUSTRY_CACHE

    if stock_code in STOCK_INDUSTRY_CACHE:
        industries = STOCK_INDUSTRY_CACHE[stock_code]
        # 过滤掉无意义的基础分类
        meaningless_keywords = {
            '其他', '未分类', '综合行业', '沪市主板', '深市主板', '深市中小板',
            '创业板', '科创板', '北交所', '沪市股票', '深市股票', 'A股市场',
            '沪市主板-传统产业', '沪市主板-金融业', '沪市主板-制造业', '沪市主板-新兴产业',
            '深市主板-综合类', '深市主板-创新类', '创业板-高科技', '创业板-新经济',
            '科创板-硬科技', '科创板-生物医药', '北交所-专精特新'
        }
        filtered_industries = [ind for ind in industries if ind not in meaningless_keywords]
        return filtered_industries if filtered_industries else []

    # 如果缓存中没有，返回空列表（不返回无意义的基础分类）
    return []


def get_stock_concept_info(stock_code: str) -> List[str]:
    """获取股票概念信息（使用缓存，过滤无意义信息）"""
    global STOCK_CONCEPT_CACHE

    if stock_code in STOCK_CONCEPT_CACHE:
        concepts = STOCK_CONCEPT_CACHE[stock_code]
        # 过滤掉无意义的基础分类
        meaningless_keywords = {
            '其他', '未分类', '综合行业', 'A股市场', '沪市股票', '深市股票',
            '中小板股票', '创业板股票', '科创板股票', '北交所股票',
            '沪市主板', '深市主板', '深市中小板', '创业板', '科创板', '北交所'
        }
        filtered_concepts = [con for con in concepts if con not in meaningless_keywords]
        return filtered_concepts if filtered_concepts else []

    # 如果缓存中没有，返回空列表（不返回无意义的基础分类）
    return []


def calculate_limit_up_threshold(stock_code: str) -> float:
    """计算涨停阈值（参考test_limit_up.py的实时监控标准）"""
    code_prefix = stock_code[:3]
    if code_prefix in ('300', '301'):  # 创业板
        return 0.195  # 19.5% (实时监控标准)
    elif code_prefix in ('688', '689'):  # 科创板
        return 0.195  # 19.5% (实时监控标准)
    elif code_prefix.startswith('8'):  # 北交所
        return 0.295  # 29.5%
    else:  # 主板、中小板
        return 0.095  # 9.5% (实时监控标准)


def calculate_market_value(stock_code: str, price: float) -> Tuple[float, float]:
    """
    计算流通市值和总市值（参考xtdata文档）

    Args:
        stock_code: 股票代码
        price: 当前价格

    Returns:
        Tuple[float, float]: (流通市值(亿), 总市值(亿))
    """
    try:
        # 获取股本数据
        detail = xtdata.get_instrument_detail(stock_code)
        if not detail:
            return 0.0, 0.0

        # 获取总股本和流通股本（单位：股）- 使用正确的字段名
        total_shares = detail.get('TotalVolume', 0)  # 总股本
        float_shares = detail.get('FloatVolume', 0)  # 流通股本

        if total_shares <= 0:
            return 0.0, 0.0

        # 计算市值（亿元）
        total_market_value = (total_shares * price) / 100000000  # 总市值
        float_market_value = (float_shares * price) / 100000000 if float_shares > 0 else total_market_value  # 流通市值

        return float_market_value, total_market_value

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 市值失败: {e}")
        return 0.0, 0.0


def calculate_turnover_rate(stock_code: str, volume: float, price: float) -> float:
    """
    计算换手率（参考xtdata文档）

    Args:
        stock_code: 股票代码
        volume: 成交量（股）
        price: 当前价格（暂未使用，保持接口一致性）

    Returns:
        float: 换手率（%）
    """
    try:
        # 获取流通股本
        detail = xtdata.get_instrument_detail(stock_code)
        if not detail:
            return 0.0

        float_shares = detail.get('FloatVolume', 0)  # 流通股本 - 使用正确的字段名

        if float_shares <= 0 or volume <= 0:
            return 0.0

        # 计算换手率 = 成交量 / 流通股本 * 100%
        turnover_rate = (volume / float_shares) * 100

        return turnover_rate

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 换手率失败: {e}")
        return 0.0


def calculate_seal_amount(stock_code: str, tick_data: Dict = None) -> float:
    """
    计算封板资金 - 通过实时盘口数据获取买一档封单金额

    Args:
        stock_code: 股票代码
        tick_data: 实时tick数据

    Returns:
        float: 封板资金（万元）
    """
    try:
        if not tick_data:
            # 获取实时盘口数据
            snapshot = xtdata.get_full_tick([stock_code])
            if stock_code not in snapshot or not snapshot[stock_code]:
                return 0.0
            tick_data = snapshot[stock_code]

        # 获取当前价格和昨收价，判断是否涨停
        last_price = tick_data.get('lastPrice', 0)
        pre_close = tick_data.get('lastClose', 0)

        if last_price <= 0 or pre_close <= 0:
            return 0.0

        # 计算涨停价
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_price = pre_close * (1 + limit_threshold)

        # 判断是否接近涨停价（允许0.01的误差）
        if abs(last_price - limit_price) > 0.01:
            return 0.0  # 不是涨停状态，没有封板资金

        # 获取买一价和买一量
        bid_prices = tick_data.get('bidPrice', [])
        bid_volumes = tick_data.get('bidVol', [])

        if not bid_prices or not bid_volumes or len(bid_prices) == 0 or len(bid_volumes) == 0:
            return 0.0

        bid_price = bid_prices[0]  # 买一价
        bid_volume = bid_volumes[0]  # 买一量

        if bid_price <= 0 or bid_volume <= 0:
            return 0.0

        # 验证买一价是否为涨停价
        if abs(bid_price - limit_price) > 0.01:
            return 0.0  # 买一价不是涨停价，不算封板

        # 计算封板资金 = 买一价 * 买一量 / 10000（万元）
        seal_amount = (bid_price * bid_volume) / 10000

        return seal_amount

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 封板资金失败: {e}")
        return 0.0


def calculate_zhaban_count(stock_code: str, date_str: str = None) -> int:
    """
    计算炸板次数 - 通过分时数据分析涨停板的封板和开板情况（优化版）

    Args:
        stock_code: 股票代码
        date_str: 日期字符串，默认为今天

    Returns:
        int: 炸板次数
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0

        df = data[stock_code]
        if len(df) == 0:
            return 0

        # 获取昨收价
        pre_close = df.iloc[0]['preClose']
        if pre_close <= 0:
            return 0

        # 根据股票类型确定涨停价格
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_price = pre_close * (1 + limit_threshold)

        zhaban_count = 0
        was_sealed = False  # 记录是否曾经封板
        consecutive_sealed_minutes = 0  # 连续封板分钟数

        # 分析每分钟的封板状态
        for _, row in df.iterrows():
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']
            volume = row['volume']
            amount = row.get('amount', 0)

            # 严格的涨停封板判断：
            # 1. 收盘价必须达到涨停价（允许0.01误差）
            # 2. 最低价也要达到涨停价（说明整个分钟都在涨停价上）
            # 3. 有成交量（说明有交易活动）
            is_sealed_now = (
                close_price >= (limit_price - 0.01) and
                low_price >= (limit_price - 0.01) and  # 关键：最低价也要在涨停价
                volume > 0
            )

            # 判断是否触及涨停但未封板（最高价达到涨停价但收盘价或最低价未达到）
            touched_but_not_sealed = (
                high_price >= (limit_price - 0.01) and
                not is_sealed_now
            )

            if is_sealed_now:
                if not was_sealed:
                    # 新的封板开始
                    was_sealed = True
                    consecutive_sealed_minutes = 1
                else:
                    # 继续封板
                    consecutive_sealed_minutes += 1
            elif touched_but_not_sealed or (was_sealed and high_price < (limit_price - 0.02)):
                # 情况1：触及涨停但未封板
                # 情况2：曾经封板但现在明显远离涨停价
                if was_sealed and consecutive_sealed_minutes >= 1:
                    # 曾经封板至少1分钟，现在开板了，算作一次炸板
                    zhaban_count += 1
                was_sealed = False
                consecutive_sealed_minutes = 0

        return zhaban_count

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 炸板次数失败: {e}")
        return 0


def calculate_zhaban_count_simple(stock_code: str, date_str: str, limit_price: float) -> int:
    """
    简化的炸板次数计算（更接近东方财富标准）
    只要触及涨停价后回落就算炸板，不要求严格的封板过程

    Args:
        stock_code: 股票代码
        date_str: 日期字符串
        limit_price: 涨停价

    Returns:
        int: 炸板次数
    """
    try:
        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0  # 无分时数据时返回0，不能假设炸板

        df = data[stock_code]
        if len(df) == 0:
            return 0

        zhaban_count = 0
        touched_limit_before = False

        # 分析每分钟的价格变化
        for _, row in df.iterrows():
            high_price = row['high']
            close_price = row['close']
            volume = row['volume']

            # 判断是否触及涨停价
            touched_limit_now = high_price >= (limit_price - 0.01) and volume > 0

            # 判断是否从涨停价回落
            fell_from_limit = (
                touched_limit_now and
                close_price < (limit_price - 0.02)  # 收盘价明显低于涨停价
            )

            if touched_limit_now:
                touched_limit_before = True

            if fell_from_limit:
                zhaban_count += 1

        # 返回实际计算出的炸板次数，如果从未开板则为0
        return zhaban_count

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 简化计算 {stock_code} 炸板次数失败: {e}")
        return 0  # 异常时返回0，不能假设炸板


def calculate_consecutive_limit_up(stock_code: str) -> int:
    """
    计算连板数 - 下载前10天交易数据进行准确计算

    Args:
        stock_code: 股票代码

    Returns:
        int: 连板数（不包括今天）
    """
    try:
        # 计算开始日期（前15天，确保包含10个交易日）
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=15)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        # 先尝试下载历史数据，确保数据完整
        try:
            xtdata.download_history_data(
                stock_code=stock_code,
                period='1d',
                start_time=start_date_str,
                end_time=end_date_str
            )
        except Exception as download_error:
            if DEBUG_PRINT:
                print(f"[DEBUG] 下载 {stock_code} 历史数据失败: {download_error}")

        # 获取历史数据
        data = xtdata.get_local_data(
            field_list=['close', 'preClose'],
            stock_list=[stock_code],
            period='1d',
            start_time=start_date_str,
            end_time=end_date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0

        df = data[stock_code].sort_index(ascending=False)  # 按时间倒序

        if len(df) < 2:
            return 0

        consecutive_days = 0
        limit_threshold = calculate_limit_up_threshold(stock_code)

        # 从昨天开始往前计算（跳过今天）
        for i in range(1, len(df)):  # 从第二行开始（跳过今天）
            row = df.iloc[i]
            close_price = row['close']
            pre_close = row['preClose']

            if close_price > 0 and pre_close > 0:
                change_rate = (close_price - pre_close) / pre_close

                if change_rate >= limit_threshold:
                    consecutive_days += 1
                else:
                    break  # 连续涨停中断
            else:
                break

        return consecutive_days

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 连板数失败: {e}")
        return 0


def get_limit_up_times(stock_code: str, date_str: str = None) -> Tuple[str, str]:
    """
    获取首次封板时间和最后封板时间 - 通过分时数据精确分析

    Args:
        stock_code: 股票代码
        date_str: 日期字符串，默认为今天

    Returns:
        Tuple[str, str]: (首次封板时间, 最后封板时间)
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return "09:25:00", "09:25:00"

        df = data[stock_code]
        if len(df) == 0:
            return "09:25:00", "09:25:00"

        # 获取昨收价和涨停价
        pre_close = df.iloc[0]['preClose']
        if pre_close <= 0:
            return "09:25:00", "09:25:00"

        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_price = pre_close * (1 + limit_threshold)

        first_seal_time = None
        last_seal_time = None

        # 分析每分钟的封板状态
        for idx, row in df.iterrows():
            close_price = row['close']
            high_price = row['high']
            volume = row['volume']

            # 判断是否封板（收盘价达到涨停价且有成交量）
            is_sealed = (close_price >= (limit_price - 0.01)) and volume > 0

            # 或者最高价触及涨停价也算封板尝试
            touched_limit = high_price >= (limit_price - 0.01)

            if is_sealed or touched_limit:
                # 获取时间字符串
                if hasattr(idx, 'strftime'):
                    time_str = idx.strftime('%H:%M:%S')
                elif hasattr(idx, 'time'):
                    time_str = idx.time().strftime('%H:%M:%S')
                else:
                    # 从索引名称中提取时间
                    time_str = str(idx)[-8:] if len(str(idx)) >= 8 else "09:25:00"
                    if ':' not in time_str:
                        time_str = "09:25:00"

                if first_seal_time is None:
                    first_seal_time = time_str

                # 只有真正封板的才更新最后封板时间
                if is_sealed:
                    last_seal_time = time_str

        return (first_seal_time or "09:25:00", last_seal_time or first_seal_time or "09:25:00")

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 获取 {stock_code} 封板时间失败: {e}")
        return "09:25:00", "09:25:00"


def calculate_limit_down_seal_amount(stock_code: str, tick_data: Dict = None) -> float:
    """
    计算跌停封单资金 - 通过实时盘口数据获取卖一档封单金额

    Args:
        stock_code: 股票代码
        tick_data: 实时tick数据

    Returns:
        float: 封单资金（万元）
    """
    try:
        if not tick_data:
            # 获取实时盘口数据
            snapshot = xtdata.get_full_tick([stock_code])
            if stock_code not in snapshot or not snapshot[stock_code]:
                return 0.0
            tick_data = snapshot[stock_code]

        # 获取当前价格和昨收价，判断是否跌停
        last_price = tick_data.get('lastPrice', 0)
        pre_close = tick_data.get('lastClose', 0)

        if last_price <= 0 or pre_close <= 0:
            return 0.0

        # 计算跌停价
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_down_price = pre_close * (1 - limit_threshold)

        # 判断是否接近跌停价（允许0.01的误差）
        if abs(last_price - limit_down_price) > 0.01:
            return 0.0  # 不是跌停状态，没有封单资金

        # 获取卖一价和卖一量
        ask_prices = tick_data.get('askPrice', [])
        ask_volumes = tick_data.get('askVol', [])

        if not ask_prices or not ask_volumes or len(ask_prices) == 0 or len(ask_volumes) == 0:
            return 0.0

        ask_price = ask_prices[0]  # 卖一价
        ask_volume = ask_volumes[0]  # 卖一量

        if ask_price <= 0 or ask_volume <= 0:
            return 0.0

        # 验证卖一价是否为跌停价
        if abs(ask_price - limit_down_price) > 0.01:
            return 0.0  # 卖一价不是跌停价，不算封板

        # 计算封单资金 = 卖一价 * 卖一量 / 10000（万元）
        seal_amount = (ask_price * ask_volume) / 10000

        return seal_amount

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 跌停封单资金失败: {e}")
        return 0.0


def get_limit_down_times(stock_code: str, date_str: str = None) -> str:
    """
    获取跌停封板时间

    Args:
        stock_code: 股票代码
        date_str: 日期字符串，默认为今天

    Returns:
        str: 最后封板时间
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return "09:25:00"

        df = data[stock_code]
        if len(df) == 0:
            return "09:25:00"

        # 获取昨收价和跌停价
        pre_close = df.iloc[0]['preClose']
        if pre_close <= 0:
            return "09:25:00"

        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_down_price = pre_close * (1 - limit_threshold)

        last_seal_time = None

        # 分析每分钟的跌停状态
        for idx, row in df.iterrows():
            close_price = row['close']
            volume = row['volume']

            # 判断是否跌停（收盘价达到跌停价且有成交量）
            is_limit_down = (close_price <= (limit_down_price + 0.01)) and volume > 0

            if is_limit_down:
                # 获取时间字符串
                if hasattr(idx, 'strftime'):
                    time_str = idx.strftime('%H:%M:%S')
                elif hasattr(idx, 'time'):
                    time_str = idx.time().strftime('%H:%M:%S')
                else:
                    # 从索引名称中提取时间
                    time_str = str(idx)[-8:] if len(str(idx)) >= 8 else "09:25:00"
                    if ':' not in time_str:
                        time_str = "09:25:00"

                last_seal_time = time_str

        return last_seal_time or "09:25:00"

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 获取 {stock_code} 跌停时间失败: {e}")
        return "09:25:00"


def calculate_consecutive_limit_down(stock_code: str) -> int:
    """
    计算连续跌停天数

    Args:
        stock_code: 股票代码

    Returns:
        int: 连续跌停天数（不包括今天）
    """
    try:
        # 计算开始日期（前15天，确保包含10个交易日）
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=15)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        # 获取历史数据
        data = xtdata.get_local_data(
            field_list=['close', 'preClose'],
            stock_list=[stock_code],
            period='1d',
            start_time=start_date_str,
            end_time=end_date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0

        df = data[stock_code].sort_index(ascending=False)  # 按时间倒序

        if len(df) < 2:
            return 0

        consecutive_days = 0
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_down_threshold = -limit_threshold  # 跌停阈值为负值

        # 从昨天开始往前计算（跳过今天）
        for i in range(1, len(df)):  # 从第二行开始（跳过今天）
            row = df.iloc[i]
            close_price = row['close']
            pre_close = row['preClose']

            if close_price > 0 and pre_close > 0:
                change_rate = (close_price - pre_close) / pre_close

                if change_rate <= limit_down_threshold:
                    consecutive_days += 1
                else:
                    break  # 连续跌停中断
            else:
                break

        return consecutive_days

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 连续跌停天数失败: {e}")
        return 0


def calculate_limit_down_open_count(stock_code: str, date_str: str = None) -> int:
    """
    计算跌停开板次数

    Args:
        stock_code: 股票代码
        date_str: 日期字符串，默认为今天

    Returns:
        int: 开板次数
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0

        df = data[stock_code]
        if len(df) == 0:
            return 0

        # 获取昨收价
        pre_close = df.iloc[0]['preClose']
        if pre_close <= 0:
            return 0

        # 根据股票类型确定跌停价格
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_down_price = pre_close * (1 - limit_threshold)

        open_count = 0
        was_limit_down = False  # 记录是否曾经跌停

        # 分析每分钟的跌停状态
        for _, row in df.iterrows():
            low_price = row['low']
            close_price = row['close']
            volume = row['volume']

            # 判断当前分钟是否跌停（收盘价达到跌停价且有成交量）
            is_limit_down_now = (close_price <= (limit_down_price + 0.01)) and volume > 0

            # 判断是否触及跌停（最低价达到跌停价）
            touched_limit_down = low_price <= (limit_down_price + 0.01)

            if touched_limit_down:
                if not is_limit_down_now and was_limit_down:
                    # 曾经跌停但现在开板了，算作一次开板
                    open_count += 1
                    was_limit_down = False
                elif is_limit_down_now:
                    was_limit_down = True

        return open_count

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 跌停开板次数失败: {e}")
        return 0


def ensure_history_data_for_consecutive_calculation(stock_codes: List[str]) -> None:
    """
    确保连板计算所需的历史数据完整性

    Args:
        stock_codes: 股票代码列表
    """
    try:
        print(">>> 检查连板计算所需的历史数据...")

        # 计算需要的日期范围（前15天）
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=15)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        # 批量下载历史数据
        batch_size = 100
        total_batches = (len(stock_codes) + batch_size - 1) // batch_size

        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            batch_num = i // batch_size + 1

            print(f">>> 下载历史数据批次 {batch_num}/{total_batches} ({len(batch_codes)} 只股票)...")

            try:
                xtdata.download_history_data(
                    stock_code=batch_codes,
                    period='1d',
                    start_time=start_date_str,
                    end_time=end_date_str
                )
            except Exception as e:
                if DEBUG_PRINT:
                    print(f"[DEBUG] 批次 {batch_num} 下载失败: {e}")
                continue

        print(">>> 历史数据检查完成")

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 确保历史数据失败: {e}")


def check_and_download_history_data(date_str: str = None) -> bool:
    """
    检查并下载历史数据（盘后使用）

    Args:
        date_str: 指定日期，格式YYYYMMDD，默认为今天

    Returns:
        bool: 是否成功下载数据
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 检查 {date_str} 的历史数据...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return False

    stock_codes = list(TARGET_STOCKS_CACHE.keys())

    # 检查数据完整性
    need_download_codes = check_data_completeness(stock_codes, date_str)

    # 判断是否需要增量下载
    today = datetime.datetime.now().strftime('%Y%m%d')
    is_today_trading_day = (date_str == today and is_trading_day(date_str))

    if not need_download_codes and not is_today_trading_day:
        print(f">>> {date_str} 的数据已完整，无需下载")
        return True
    elif not need_download_codes and is_today_trading_day:
        print(f">>> 当天交易日，执行增量下载更新所有股票数据")
        # 使用增量下载模式，不指定具体股票列表
        return perform_incremental_download(date_str)
    else:
        print(f">>> 需要下载 {len(need_download_codes)} 只股票的历史数据")

    # 下载历史数据
    success_count = 0
    error_count = 0
    batch_size = 100  # 增加批次大小提高效率

    for i in tqdm(range(0, len(need_download_codes), batch_size), desc="下载历史数据"):
        batch_codes = need_download_codes[i:i + batch_size]

        # 尝试批量下载
        try:
            for code in batch_codes:
                xtdata.download_history_data(
                    stock_code=code,
                    period='1d',
                    start_time=date_str,
                    end_time=date_str,
                    incrementally=True
                )
                success_count += 1
        except Exception as e:
            # 如果批量下载失败，逐个下载
            for code in batch_codes:
                try:
                    xtdata.download_history_data(
                        stock_code=code,
                        period='1d',
                        start_time=date_str,
                        end_time=date_str,
                        incrementally=True
                    )
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    if DEBUG_PRINT and error_count <= 10:
                        print(f"[DEBUG] 下载 {code} 失败: {e}")

        # 每批次后稍作休息
        if i < len(need_download_codes) - batch_size:
            time.sleep(0.05)  # 减少休息时间

    print(f">>> 历史数据下载完成: 成功 {success_count}, 失败 {error_count}")
    return success_count > 0


def perform_incremental_download(date_str: str) -> bool:
    """
    执行增量下载，更新所有股票的当日数据

    Args:
        date_str: 日期字符串

    Returns:
        bool: 是否成功
    """
    try:
        print(">>> 开始增量下载...")

        # 使用增量下载，不指定股票列表，让xtdata自动处理
        # 根据文档，当start_time为空时会自动增量下载
        xtdata.download_history_data(
            stock_code='',  # 空字符串表示所有股票
            period='1d',
            start_time='',  # 空字符串启用增量下载
            end_time=date_str,
            incrementally=True
        )

        print(">>> 增量下载完成")
        return True

    except Exception as e:
        print(f">>> 增量下载失败，回退到逐个下载模式: {e}")

        # 回退到传统下载模式
        if not TARGET_STOCKS_CACHE:
            load_target_stocks()

        stock_codes = list(TARGET_STOCKS_CACHE.keys())
        success_count = 0
        error_count = 0

        print(f">>> 回退模式：下载 {len(stock_codes)} 只股票")

        for code in tqdm(stock_codes[:100], desc="回退下载"):  # 限制数量避免过长时间
            try:
                xtdata.download_history_data(
                    stock_code=code,
                    period='1d',
                    start_time=date_str,
                    end_time=date_str,
                    incrementally=True
                )
                success_count += 1
            except Exception:
                error_count += 1

        print(f">>> 回退下载完成: 成功 {success_count}, 失败 {error_count}")
        return success_count > 0


def check_data_completeness(stock_codes: List[str], date_str: str) -> List[str]:
    """
    检查数据完整性，返回需要下载的股票代码列表

    Args:
        stock_codes: 股票代码列表
        date_str: 检查的日期

    Returns:
        List[str]: 需要下载的股票代码列表
    """
    # 对于当天交易日，使用增量下载而不是强制下载所有股票
    today = datetime.datetime.now().strftime('%Y%m%d')
    if date_str == today and is_trading_day(date_str):
        print(f">>> 当天交易日，使用增量下载更新数据")
        # 返回空列表，让下载函数使用增量模式
        return []

    need_download = []

    # 随机抽样检查，提高效率
    import random
    sample_size = min(50, len(stock_codes))
    sample_codes = random.sample(stock_codes, sample_size)

    missing_count = 0
    for code in sample_codes:
        try:
            data = xtdata.get_local_data(
                field_list=['close'],
                stock_list=[code],
                period='1d',
                start_time=date_str,
                end_time=date_str,
                dividend_type='none'
            )

            if code not in data or len(data[code]) == 0:
                missing_count += 1
        except Exception:
            missing_count += 1

    # 如果超过30%的样本缺少数据，则需要下载所有股票
    missing_ratio = missing_count / sample_size
    if DEBUG_PRINT:
        print(f"[DEBUG] 数据完整性检查: {missing_count}/{sample_size} 缺失 ({missing_ratio:.1%})")

    if missing_ratio > 0.3:
        print(f">>> 检测到 {missing_ratio:.1%} 的数据缺失，需要下载历史数据")
        return stock_codes
    else:
        print(">>> 历史数据基本完整")
        return []


def get_current_limit_up_stocks(date_str: str = None) -> List[Dict]:
    """
    获取当天所有涨停股票

    Args:
        date_str: 指定日期，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 涨停股票列表，包含所有需要的字段
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取 {date_str} 的涨停股票...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据
    download_sector_data_if_needed()

    # 确保连板计算所需的历史数据
    stock_codes = list(TARGET_STOCKS_CACHE.keys())
    if stock_codes:
        ensure_history_data_for_consecutive_calculation(stock_codes[:200])  # 限制数量避免过长时间

    # 如果是盘后且是交易日，先检查并下载历史数据
    if is_after_trading_hours() and is_trading_day(date_str):
        print(">>> 检测到盘后时间，检查历史数据...")
        check_and_download_history_data(date_str)
    # 如果是非交易时间但是交易日，也要下载历史数据
    elif not is_trading_time() and is_trading_day(date_str):
        print(">>> 检测到非交易时间但是交易日，下载历史数据...")
        check_and_download_history_data(date_str)

    limit_up_stocks = []
    stock_codes = list(TARGET_STOCKS_CACHE.keys())

    print(f">>> 分析 {len(stock_codes)} 只股票的涨停情况...")

    # 分批处理，提高效率
    batch_size = 100
    for i in range(0, len(stock_codes), batch_size):
        batch_codes = stock_codes[i:i + batch_size]

        try:
            # 获取当日数据
            data = xtdata.get_local_data(
                field_list=['close', 'preClose', 'open', 'high', 'low', 'volume', 'amount'],
                stock_list=batch_codes,
                period='1d',
                start_time=date_str,
                end_time=date_str,
                dividend_type='none'
            )

            for code in batch_codes:
                if code in data and len(data[code]) > 0:
                    df = data[code]
                    if len(df) > 0:
                        latest_data = df.iloc[-1]
                        close_price = latest_data['close']
                        pre_close = latest_data['preClose']

                        if close_price > 0 and pre_close > 0:
                            change_rate = (close_price - pre_close) / pre_close
                            limit_threshold = calculate_limit_up_threshold(code)

                            # 判断是否涨停
                            if change_rate >= limit_threshold:
                                detail = TARGET_STOCKS_CACHE.get(code, {})

                                # 计算各种指标
                                volume = latest_data.get('volume', 0)
                                amount = latest_data.get('amount', 0)

                                # 计算市值
                                float_market_value, total_market_value = calculate_market_value(code, close_price)

                                # 计算换手率
                                turnover_rate = calculate_turnover_rate(code, volume, close_price)

                                # 计算炸板次数
                                zhaban_count = calculate_zhaban_count(code, date_str)

                                # 计算连板数
                                consecutive_days = calculate_consecutive_limit_up(code)

                                # 获取封板时间
                                first_seal_time, last_seal_time = get_limit_up_times(code, date_str)

                                # 构建涨停股票信息
                                stock_info = {
                                    '序号': len(limit_up_stocks) + 1,
                                    '代码': code,
                                    '名称': detail.get('InstrumentName', 'N/A'),
                                    '涨跌幅': f"{change_rate:.2%}",
                                    '最新价': f"{close_price:.2f}",
                                    '成交额': f"{amount / 10000:.0f}万" if amount > 0 else '-',
                                    '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                                    '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                                    '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                                    '封板资金': '-',  # 历史数据无法获取实时封板资金
                                    '首次封板时间': first_seal_time,
                                    '最后封板时间': last_seal_time,
                                    '炸板次数': f"{zhaban_count}次",
                                    '涨停统计': '1/1',  # 简化处理
                                    '连板数': f"{consecutive_days + 1}连板" if consecutive_days > 0 else '首板',
                                    '所属行业': ', '.join(get_stock_industry_info(code)[:2]),  # 只显示前2个
                                    '所属概念': ', '.join(get_stock_concept_info(code)[:3]),  # 显示前3个概念
                                }

                                limit_up_stocks.append(stock_info)

        except Exception as e:
            if DEBUG_PRINT:
                print(f"[DEBUG] 处理批次 {i//batch_size + 1} 失败: {e}")
            continue

    # 按涨幅排序
    limit_up_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')), reverse=True)

    # 更新序号
    for i, stock in enumerate(limit_up_stocks):
        stock['序号'] = i + 1

    print(f">>> 找到 {len(limit_up_stocks)} 只涨停股票")

    # 保存结果
    save_limit_up_data(limit_up_stocks, date_str)

    # 新增：主线识别分析
    if limit_up_stocks:
        print(f"\n>>> 开始主线识别分析...")
        main_theme_analysis = generate_main_theme_analysis(limit_up_stocks)
        print_main_theme_analysis(main_theme_analysis)

    return limit_up_stocks


def get_zhaban_stocks(date_str: str = None) -> List[Dict]:
    """
    获取炸板股票池

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 炸板股票列表，包含完整信息
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取 {date_str} 的炸板股票...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据
    if not STOCK_INDUSTRY_CACHE or not STOCK_CONCEPT_CACHE:
        download_sector_data_if_needed()

    zhaban_stocks = []

    try:
        # 获取历史数据
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        print(f">>> 分析 {len(data)} 只股票的炸板情况...")

        for code, stock_data in data.items():
            if len(stock_data) == 0:
                continue

            latest_data = stock_data.iloc[-1]
            open_price = latest_data['open']
            high_price = latest_data['high']
            low_price = latest_data['low']
            close_price = latest_data['close']
            volume = latest_data['volume']
            amount = latest_data['amount']
            pre_close = latest_data['preClose']

            if close_price <= 0 or pre_close <= 0:
                continue

            # 计算涨跌幅
            change_rate = (close_price - pre_close) / pre_close

            # 计算涨停价
            limit_threshold = calculate_limit_up_threshold(code)
            limit_price = pre_close * (1 + limit_threshold)

            # 炸板判断：精确匹配东方财富标准
            # 1. 最高价必须触及涨停价
            # 2. 收盘价低于涨停价（允许较小差距）
            # 3. 有足够的成交量和成交额
            # 4. 涨跌幅范围：1.5% - 12%
            reached_limit = high_price >= (limit_price - 0.01)
            not_closed_limit = close_price < (limit_price - 0.01)  # 简单的未涨停判断
            has_volume = volume > 0 and amount > 100000  # 至少10万元成交额
            reasonable_rise = change_rate >= 0.015  # 至少1.5%涨幅（包含东财的低涨幅股票）
            not_too_high = change_rate <= 0.12  # 不超过12%涨幅

            if reached_limit and not_closed_limit and has_volume and reasonable_rise and not_too_high:
                # 计算炸板次数（通过分时数据）
                zhaban_count_today = calculate_zhaban_count_simple(code, date_str, limit_price)

                # 只有真正发生炸板（炸板次数>0）的股票才加入炸板股票池
                if zhaban_count_today <= 0:
                    continue

                detail = TARGET_STOCKS_CACHE.get(code, {})

                # 计算各种指标
                float_market_value, total_market_value = calculate_market_value(code, close_price)
                turnover_rate = calculate_turnover_rate(code, volume, close_price)
                # 使用之前计算的炸板次数
                zhaban_count = zhaban_count_today
                first_seal_time, _ = get_limit_up_times(code, date_str)

                # 计算涨速（简化处理）
                rise_speed = change_rate * 100  # 当日涨跌幅作为涨速

                # 计算振幅
                amplitude = ((high_price - low_price) / pre_close) * 100 if pre_close > 0 else 0

                # 构建炸板股票信息
                stock_info = {
                    '序号': len(zhaban_stocks) + 1,
                    '代码': code,
                    '名称': detail.get('InstrumentName', 'N/A'),
                    '涨跌幅': f"{change_rate:.2%}",
                    '最新价': f"{close_price:.2f}",
                    '涨停价': f"{limit_price:.2f}",
                    '成交额': f"{amount / 10000:.0f}万" if amount > 0 else '-',
                    '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                    '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                    '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                    '涨速': f"{rise_speed:.2f}%",
                    '首次封板时间': first_seal_time,
                    '炸板次数': f"{zhaban_count}次",
                    '涨停统计': '1/1',  # 简化处理
                    '振幅': f"{amplitude:.2f}%",
                    '所属行业': ', '.join(get_stock_industry_info(code)[:2]),
                }

                zhaban_stocks.append(stock_info)

        # 按涨跌幅排序
        zhaban_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')), reverse=True)

        # 重新编号
        for i, stock in enumerate(zhaban_stocks):
            stock['序号'] = i + 1

        print(f">>> 找到 {len(zhaban_stocks)} 只炸板股票")

        # 保存数据
        save_zhaban_data(zhaban_stocks, date_str)

    except Exception as e:
        print(f">>> 获取炸板股票失败: {e}")
        return []

    return zhaban_stocks


def get_limit_down_stocks(date_str: str = None) -> List[Dict]:
    """
    获取跌停板股票池

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 跌停股票列表，包含完整信息
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取 {date_str} 的跌停股票...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据
    if not STOCK_INDUSTRY_CACHE or not STOCK_CONCEPT_CACHE:
        download_sector_data_if_needed()

    limit_down_stocks = []

    try:
        # 获取历史数据
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        print(f">>> 分析 {len(data)} 只股票的跌停情况...")

        for code, stock_data in data.items():
            if len(stock_data) == 0:
                continue

            latest_data = stock_data.iloc[-1]
            close_price = latest_data['close']
            volume = latest_data['volume']
            amount = latest_data['amount']
            pre_close = latest_data['preClose']

            if close_price <= 0 or pre_close <= 0:
                continue

            # 计算涨跌幅
            change_rate = (close_price - pre_close) / pre_close

            # 计算跌停阈值（负值）
            limit_threshold = calculate_limit_up_threshold(code)  # 获取涨停阈值
            limit_down_threshold = -limit_threshold  # 跌停阈值为负值

            # 判断是否跌停
            if change_rate <= limit_down_threshold:
                detail = TARGET_STOCKS_CACHE.get(code, {})

                # 计算各种指标
                float_market_value, total_market_value = calculate_market_value(code, close_price)
                turnover_rate = calculate_turnover_rate(code, volume, close_price)

                # 计算动态市盈率（简化处理，需要财务数据）
                pe_ratio = 0.0  # 暂时设为0，需要财务数据支持

                # 计算封单资金（跌停时为卖一档）
                seal_amount = calculate_limit_down_seal_amount(code)

                # 获取跌停时间
                last_seal_time = get_limit_down_times(code, date_str)

                # 计算板上成交额（简化处理）
                board_amount = amount * 0.1  # 假设10%的成交在跌停板上

                # 计算连续跌停天数
                consecutive_days = calculate_consecutive_limit_down(code)

                # 计算开板次数（简化处理）
                open_count = calculate_limit_down_open_count(code, date_str)

                # 构建跌停股票信息
                stock_info = {
                    '序号': len(limit_down_stocks) + 1,
                    '代码': code,
                    '名称': detail.get('InstrumentName', 'N/A'),
                    '涨跌幅': f"{change_rate:.2%}",
                    '最新价': f"{close_price:.2f}",
                    '成交额': f"{amount / 10000:.0f}万" if amount > 0 else '-',
                    '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                    '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                    '动态市盈率': f"{pe_ratio:.2f}" if pe_ratio > 0 else '-',
                    '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                    '封单资金': f"{seal_amount:.0f}万" if seal_amount > 0 else '-',
                    '最后封板时间': last_seal_time,
                    '板上成交额': f"{board_amount / 10000:.0f}万" if board_amount > 0 else '-',
                    '连续跌停': f"{consecutive_days + 1}天" if consecutive_days > 0 else '1天',
                    '开板次数': f"{open_count}次",
                    '所属行业': ', '.join(get_stock_industry_info(code)[:2]),
                }

                limit_down_stocks.append(stock_info)

        # 按涨跌幅排序（从低到高）
        limit_down_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')))

        # 重新编号
        for i, stock in enumerate(limit_down_stocks):
            stock['序号'] = i + 1

        print(f">>> 找到 {len(limit_down_stocks)} 只跌停股票")

        # 保存数据
        save_limit_down_data(limit_down_stocks, date_str)

    except Exception as e:
        print(f">>> 获取跌停股票失败: {e}")
        return []

    return limit_down_stocks


def get_yesterday_limit_up_stocks(date_str: str = None) -> List[Dict]:
    """
    获取昨日涨停股票今日表现复盘 - 增强版

    Args:
        date_str: 今日日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 昨日涨停股票今日表现复盘列表，包含完整的复盘数据
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取昨日涨停股票今日表现复盘...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据
    if not STOCK_INDUSTRY_CACHE or not STOCK_CONCEPT_CACHE:
        download_sector_data_if_needed()

    yesterday_limit_up_stocks = []

    try:
        # 获取昨天的日期
        current_date = datetime.datetime.strptime(date_str, '%Y%m%d')
        yesterday = current_date - datetime.timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y%m%d')

        # 获取昨日数据
        yesterday_data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=yesterday_str,
            end_time=yesterday_str,
            dividend_type='none'
        )

        # 获取今日数据用于计算当前状态
        today_data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if not yesterday_data or not today_data:
            print(">>> 无法获取历史数据")
            return []

        # 获取今日涨停股票池（用于晋级结果判断）
        today_limit_up_stocks = get_current_limit_up_stocks(date_str)
        today_limit_up_codes = {stock['代码']: stock for stock in today_limit_up_stocks}

        # 找出昨天的涨停股票
        yesterday_limit_up_codes = []
        for stock_code in TARGET_STOCKS_CACHE.keys():
            if stock_code in yesterday_data and len(yesterday_data[stock_code]) > 0:
                yesterday_close = yesterday_data[stock_code].iloc[0]['close']
                yesterday_pre_close = yesterday_data[stock_code].iloc[0]['preClose']

                if yesterday_close > 0 and yesterday_pre_close > 0:
                    change_rate = (yesterday_close - yesterday_pre_close) / yesterday_pre_close
                    limit_threshold = calculate_limit_up_threshold(stock_code)

                    if change_rate >= limit_threshold:
                        yesterday_limit_up_codes.append(stock_code)

        print(f">>> 找到昨日涨停股票 {len(yesterday_limit_up_codes)} 只")

        # 分析这些股票今日的表现
        for stock_code in yesterday_limit_up_codes:
            stock_info = TARGET_STOCKS_CACHE.get(stock_code, {})
            stock_name = stock_info.get('name', stock_code)

            # 获取今日数据
            if stock_code in today_data and len(today_data[stock_code]) > 0:
                today_close = today_data[stock_code].iloc[0]['close']
                today_pre_close = today_data[stock_code].iloc[0]['preClose']
                today_volume = today_data[stock_code].iloc[0]['volume']
                today_amount = today_data[stock_code].iloc[0]['amount']

                if today_close > 0 and today_pre_close > 0:
                    today_change_rate = (today_close - today_pre_close) / today_pre_close
                    today_change_pct = today_change_rate * 100

                    # 计算昨日连板数
                    yesterday_boards = calculate_consecutive_limit_up(stock_code)

                    # 分析晋级结果
                    promotion_result = analyze_promotion_result(stock_code, yesterday_boards, today_limit_up_codes, today_change_pct)

                    # 计算市场地位
                    market_position = calculate_market_position(yesterday_boards, promotion_result)

                    # 获取竞价表现
                    auction_performance = get_auction_performance(stock_code, date_str, today_pre_close)

                    # 获取分时强度
                    intraday_strength = get_intraday_strength(stock_code, date_str)

                    # 获取封单强度
                    seal_strength = get_seal_strength(stock_code, date_str, today_limit_up_codes)

                    # 计算收盘溢价
                    closing_premium = calculate_closing_premium(today_close, today_pre_close)

                    # 获取核心题材
                    core_concepts = get_stock_concept_info(stock_code)[:3]  # 取前3个概念

                    # 生成盘口语言解读
                    market_interpretation = generate_market_interpretation(
                        stock_code, stock_name, market_position, promotion_result,
                        auction_performance, intraday_strength, seal_strength
                    )

                    stock_data = {
                        '代码': stock_code,
                        '名称': stock_name,
                        '昨日连板数': f"{yesterday_boards}板",
                        '市场地位': market_position,
                        '晋级结果': promotion_result,
                        '竞价表现': auction_performance,
                        '分时强度': intraday_strength,
                        '封单强度': seal_strength,
                        '收盘溢价': closing_premium,
                        '涨跌幅': f"{today_change_pct:.2f}%",
                        '最新价': f"{today_close:.2f}",
                        '成交额': f"{today_amount / 10000:.0f}万" if today_amount > 0 else '-',
                        '核心题材': '/'.join(core_concepts) if core_concepts else '未知',
                        '所属行业': get_stock_industry_info(stock_code),
                        '盘口语言解读': market_interpretation
                    }

                    yesterday_limit_up_stocks.append(stock_data)

        print(f">>> 成功获取昨日涨停股票今日表现复盘 {len(yesterday_limit_up_stocks)} 只")

    except Exception as e:
        print(f">>> 获取昨日涨停股票失败: {e}")
        return []

    return yesterday_limit_up_stocks


# ==================== 主线识别模块 ====================

def _parse_metric_to_float(metric_str: str) -> float:
    """
    解析指标字符串为浮点数
    如"15.8亿" -> 1580000000.0, "5.2%" -> 5.2, "3000万" -> 30000000.0

    Args:
        metric_str: 指标字符串

    Returns:
        float: 解析后的数值
    """
    if not metric_str or metric_str == 'N/A':
        return 0.0

    try:
        # 移除空格
        metric_str = str(metric_str).strip()

        # 处理百分比
        if '%' in metric_str:
            return float(metric_str.replace('%', ''))

        # 处理亿、万单位
        if '亿' in metric_str:
            return float(metric_str.replace('亿', '')) * 100000000
        elif '万' in metric_str:
            return float(metric_str.replace('万', '')) * 10000
        else:
            # 尝试直接转换为浮点数
            return float(metric_str)
    except:
        return 0.0


def _parse_consecutive_days(consecutive_info: str) -> int:
    """
    解析连板数信息

    Args:
        consecutive_info: 连板信息，如"3连板"、"首板"

    Returns:
        int: 连板数
    """
    if isinstance(consecutive_info, str):
        if '首板' in consecutive_info:
            return 1
        else:
            try:
                # 提取数字，如"3连板" -> 3
                return int(consecutive_info.replace('连板', ''))
            except:
                return 1
    else:
        # 如果是数字类型
        return int(consecutive_info) if consecutive_info else 1


def get_theme_mapping() -> Dict[str, List[str]]:
    """
    获取题材知识库映射
    将高度关联的概念和行业进行归类

    Returns:
        Dict[str, List[str]]: 题材映射字典
    """
    return {
        "大基建": ["工程建设", "水泥", "建筑材料", "专用设备", "钢铁", "建筑装饰", "建材", "基础建设"],
        "新质生产力": ["人工智能", "工业母机", "机器人", "高端制造", "智能制造", "工业4.0"],
        "大消费": ["白酒", "食品饮料", "旅游酒店", "商业百货", "消费电子", "新零售"],
        "新能源赛道": ["光伏", "储能", "锂电池", "新能源车", "新能源汽车", "充电桩", "风电"],
        "医药健康": ["生物医药", "创新药", "疫苗", "医疗器械", "中药", "化学制药"],
        "科技创新": ["芯片", "半导体", "5G", "物联网", "大数据", "云计算", "区块链"],
        "军工国防": ["军工", "国防军工", "航空航天", "军工电子", "船舶制造"],
        "金融地产": ["银行", "保险", "证券", "房地产", "信托", "租赁"],
        "资源能源": ["煤炭", "石油石化", "有色金属", "钢铁", "化工", "天然气"],
        "数字经济": ["互联网", "电子商务", "数字货币", "元宇宙", "游戏", "传媒"],
        "环保节能": ["环保", "节能", "碳中和", "氢能源", "清洁能源", "污水处理"],
        "交通运输": ["交通运输", "物流", "航空", "铁路", "港口", "快递"]
    }


def analyze_sector_concentration(limit_up_stocks: List[Dict]) -> Dict[str, Dict]:
    """
    第一步：聚气 - 识别板块效应
    分析涨停股票的板块集中度，找出资金聚集的方向

    Args:
        limit_up_stocks: 涨停股票列表

    Returns:
        Dict[str, Dict]: 板块分析结果
    """
    if not limit_up_stocks:
        return {}

    print(">>> 【聚气】开始识别板块效应...")

    # 获取题材映射
    theme_map = get_theme_mapping()

    # 统计原始概念和行业
    industry_count = {}
    concept_count = {}
    theme_count = {}

    for stock in limit_up_stocks:
        # 处理行业信息
        industries = stock.get('所属行业', '').split(', ') if stock.get('所属行业') else []
        for industry in industries:
            if industry and industry.strip():
                industry = industry.strip()
                industry_count[industry] = industry_count.get(industry, 0) + 1

        # 处理概念信息
        concepts = stock.get('所属概念', '').split(', ') if stock.get('所属概念') else []
        for concept in concepts:
            if concept and concept.strip():
                concept = concept.strip()
                concept_count[concept] = concept_count.get(concept, 0) + 1

        # 归类到大题材
        all_sectors = industries + concepts
        for theme, keywords in theme_map.items():
            for sector in all_sectors:
                if any(keyword in sector for keyword in keywords):
                    theme_count[theme] = theme_count.get(theme, 0) + 1
                    break

    # 筛选有效板块（至少4家涨停）
    threshold = 4
    effective_themes = {k: v for k, v in theme_count.items() if v >= threshold}
    effective_industries = {k: v for k, v in industry_count.items() if v >= threshold}
    effective_concepts = {k: v for k, v in concept_count.items() if v >= threshold}

    result = {
        "大题材": dict(sorted(effective_themes.items(), key=lambda x: x[1], reverse=True)),
        "细分行业": dict(sorted(effective_industries.items(), key=lambda x: x[1], reverse=True)),
        "热点概念": dict(sorted(effective_concepts.items(), key=lambda x: x[1], reverse=True)),
        "统计信息": {
            "总涨停数": len(limit_up_stocks),
            "有效题材数": len(effective_themes),
            "筛选阈值": threshold
        }
    }

    print(f">>> 【聚气完成】发现 {len(effective_themes)} 个有效大题材，{len(effective_industries)} 个细分行业")
    return result


def analyze_ladder_structure(limit_up_stocks: List[Dict], sector_analysis: Dict) -> Dict[str, Dict]:
    """
    第二步：成阵 - 分析梯队结构
    分析每个主线板块的连板梯队结构是否健康

    Args:
        limit_up_stocks: 涨停股票列表
        sector_analysis: 板块分析结果

    Returns:
        Dict[str, Dict]: 梯队结构分析结果
    """
    if not limit_up_stocks or not sector_analysis.get("大题材"):
        return {}

    print(">>> 【成阵】开始分析梯队结构...")

    theme_map = get_theme_mapping()
    ladder_analysis = {}

    # 为每个有效大题材分析梯队结构
    for theme, count in sector_analysis["大题材"].items():
        theme_stocks = []

        # 找出属于该题材的涨停股
        for stock in limit_up_stocks:
            industries = stock.get('所属行业', '').split(', ') if stock.get('所属行业') else []
            concepts = stock.get('所属概念', '').split(', ') if stock.get('所属概念') else []
            all_sectors = industries + concepts

            # 检查是否属于该题材
            belongs_to_theme = False
            for sector in all_sectors:
                if any(keyword in sector for keyword in theme_map.get(theme, [])):
                    belongs_to_theme = True
                    break

            if belongs_to_theme:
                theme_stocks.append(stock)

        if not theme_stocks:
            continue

        # 分析连板梯队
        ladder_stats = {}
        for stock in theme_stocks:
            # 修复：使用正确的字段名 '连板数' 而不是 '连板天数'
            consecutive_info = stock.get('连板数', '首板')
            if isinstance(consecutive_info, str):
                # 处理"首板"、"2连板"等字符串格式
                if '首板' in consecutive_info:
                    consecutive_days = 1
                else:
                    try:
                        # 提取数字，如"3连板" -> 3
                        consecutive_days = int(consecutive_info.replace('连板', ''))
                    except:
                        consecutive_days = 1
            else:
                # 如果是数字类型
                consecutive_days = int(consecutive_info) if consecutive_info else 1

            ladder_stats[consecutive_days] = ladder_stats.get(consecutive_days, 0) + 1

        # 计算梯队评分
        max_height = max(ladder_stats.keys()) if ladder_stats else 0
        height_score = min(max_height * 10, 50)  # 高度分，最高50分

        # 厚度分：2板以上的家数
        thickness_score = sum(count for level, count in ladder_stats.items() if level >= 2) * 5

        # 广度分：首板家数
        width_score = ladder_stats.get(1, 0) * 2

        # 健康度：检查是否有断层
        health_score = 20  # 基础分
        if max_height > 2:
            # 检查是否有中间连板
            has_middle = any(level in ladder_stats for level in range(2, max_height))
            if not has_middle:
                health_score -= 10  # 断层扣分

        total_score = height_score + thickness_score + width_score + health_score

        ladder_analysis[theme] = {
            "股票数量": len(theme_stocks),
            "最高连板": max_height,
            "梯队分布": dict(sorted(ladder_stats.items(), reverse=True)),
            "评分详情": {
                "高度分": height_score,
                "厚度分": thickness_score,
                "广度分": width_score,
                "健康度": health_score,
                "总分": total_score
            },
            "代表股票": [
                {
                    "代码": stock.get('代码', ''),
                    "名称": stock.get('名称', ''),
                    "连板数": _parse_consecutive_days(stock.get('连板数', '首板')),
                    "涨停时间": stock.get('首次封板时间', ''),
                    "成交额": stock.get('成交额', '')
                }
                for stock in sorted(theme_stocks,
                                  key=lambda x: (_parse_consecutive_days(x.get('连板数', '首板')), x.get('首次封板时间', '99:99:99')),
                                  reverse=True)[:5]
            ]
        }

    # 按总分排序
    ladder_analysis = dict(sorted(ladder_analysis.items(),
                                key=lambda x: x[1]["评分详情"]["总分"],
                                reverse=True))

    print(f">>> 【成阵完成】分析了 {len(ladder_analysis)} 个题材的梯队结构")
    return ladder_analysis


def identify_core_leaders(limit_up_stocks: List[Dict], ladder_analysis: Dict) -> Dict[str, Dict]:
    """
    第三步：点将 - 锁定核心高标
    在最强的1-2个主线中，识别出核心龙头股

    Args:
        limit_up_stocks: 涨停股票列表
        ladder_analysis: 梯队结构分析结果

    Returns:
        Dict[str, Dict]: 核心龙头识别结果
    """
    if not ladder_analysis:
        return {}

    print(">>> 【点将】开始锁定核心高标...")

    # 选择评分最高的1-2个主线
    top_themes = list(ladder_analysis.keys())[:2]
    leaders_analysis = {}

    for theme in top_themes:
        theme_data = ladder_analysis[theme]
        max_height = theme_data["最高连板"]

        # 找出该题材的最高连板股票
        theme_stocks = theme_data["代表股票"]
        highest_stocks = [stock for stock in theme_stocks if stock["连板数"] == max_height]

        if not highest_stocks:
            continue

        # 如果只有一只最高板，直接确定为龙头
        if len(highest_stocks) == 1:
            leader = highest_stocks[0]
        else:
            # 多只同高度股票，需要进一步筛选
            # 找到这些股票的完整信息进行综合排序
            highest_stocks_details = []
            for stock_info in highest_stocks:
                for stock in limit_up_stocks:
                    if stock.get('代码') == stock_info['代码']:
                        highest_stocks_details.append(stock)
                        break

            if highest_stocks_details:
                # 核心排序逻辑：
                # 1. 首次封板时间 (升序) -> 越早越好
                # 2. 换手率 (降序) -> 换手充分者为佳
                # 3. 成交额 (降序) -> 承载资金多者为重
                leader_detail = sorted(highest_stocks_details, key=lambda x: (
                    x.get('首次封板时间', '99:99:99'),
                    -_parse_metric_to_float(x.get('换手率', '0%')),
                    -_parse_metric_to_float(x.get('成交额', '0万'))
                ))[0]

                # 转换为简化格式
                leader = {
                    '代码': leader_detail.get('代码', ''),
                    '名称': leader_detail.get('名称', ''),
                    '连板数': _parse_consecutive_days(leader_detail.get('连板数', '首板')),
                    '涨停时间': leader_detail.get('首次封板时间', ''),
                    '成交额': leader_detail.get('成交额', '')
                }
            else:
                # 回退到简单排序
                leader = min(highest_stocks, key=lambda x: x.get('涨停时间', '99:99:99'))

        # 获取详细信息
        leader_detail = None
        for stock in limit_up_stocks:
            if stock.get('代码') == leader['代码']:
                leader_detail = stock
                break

        if leader_detail:
            leaders_analysis[theme] = {
                "龙头股票": {
                    "代码": leader['代码'],
                    "名称": leader['名称'],
                    "连板数": leader['连板数'],
                    "涨停时间": leader.get('涨停时间', ''),
                    "成交额": leader_detail.get('成交额', ''),
                    "换手率": leader_detail.get('换手率', ''),
                    "封板资金": leader_detail.get('封板资金', ''),
                    "炸板次数": leader_detail.get('炸板次数', 0)
                },
                "题材强度": theme_data["评分详情"]["总分"],
                "题材股票数": theme_data["股票数量"],
                "梯队健康度": "健康" if theme_data["评分详情"]["健康度"] >= 15 else "一般"
            }

    print(f">>> 【点将完成】锁定了 {len(leaders_analysis)} 个主线的核心龙头")
    return leaders_analysis


def generate_main_theme_analysis(limit_up_stocks: List[Dict]) -> Dict:
    """
    生成完整的主线识别分析报告

    Args:
        limit_up_stocks: 涨停股票列表

    Returns:
        Dict: 完整的主线分析报告
    """
    if not limit_up_stocks:
        return {
            "分析时间": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "涨停总数": 0,
            "主线识别": "无涨停股票，无法进行主线识别"
        }

    print(f"\n{'='*60}")
    print("🔍 开始主线识别分析...")
    print(f"{'='*60}")

    # 第一步：聚气 - 识别板块效应
    sector_analysis = analyze_sector_concentration(limit_up_stocks)

    # 第二步：成阵 - 分析梯队结构
    ladder_analysis = analyze_ladder_structure(limit_up_stocks, sector_analysis)

    # 第三步：点将 - 锁定核心高标
    leaders_analysis = identify_core_leaders(limit_up_stocks, ladder_analysis)

    # 生成综合分析报告
    analysis_result = {
        "分析时间": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "涨停总数": len(limit_up_stocks),
        "聚气结果": sector_analysis,
        "成阵结果": ladder_analysis,
        "点将结果": leaders_analysis,
        "市场情绪": generate_market_sentiment_analysis(limit_up_stocks, ladder_analysis)
    }

    # 缓存结果
    MAIN_THEME_CACHE[datetime.datetime.now().strftime('%Y%m%d')] = analysis_result

    return analysis_result


def generate_market_sentiment_analysis(limit_up_stocks: List[Dict], ladder_analysis: Dict) -> Dict:
    """
    生成市场情绪分析

    Args:
        limit_up_stocks: 涨停股票列表
        ladder_analysis: 梯队分析结果

    Returns:
        Dict: 市场情绪分析结果
    """
    total_stocks = len(limit_up_stocks)
    if total_stocks == 0:
        return {"情绪等级": "冰点", "描述": "无涨停股票"}

    # 统计连板分布
    consecutive_stats = {}
    for stock in limit_up_stocks:
        # 修复：使用正确的字段名和解析函数
        consecutive_days = _parse_consecutive_days(stock.get('连板数', '首板'))
        consecutive_stats[consecutive_days] = consecutive_stats.get(consecutive_days, 0) + 1

    # 计算情绪指标
    max_consecutive = max(consecutive_stats.keys()) if consecutive_stats else 1
    high_board_count = sum(count for level, count in consecutive_stats.items() if level >= 3)
    first_board_ratio = consecutive_stats.get(1, 0) / total_stocks

    # 判断情绪等级
    if total_stocks >= 100 and max_consecutive >= 5:
        sentiment = "亢奋"
    elif total_stocks >= 50 and max_consecutive >= 4:
        sentiment = "活跃"
    elif total_stocks >= 30 and max_consecutive >= 3:
        sentiment = "温和"
    elif total_stocks >= 10:
        sentiment = "平淡"
    else:
        sentiment = "冰点"

    return {
        "情绪等级": sentiment,
        "涨停总数": total_stocks,
        "最高连板": max_consecutive,
        "高标数量": high_board_count,
        "首板占比": f"{first_board_ratio:.1%}",
        "描述": f"市场共有{total_stocks}只涨停股，最高{max_consecutive}连板，高标{high_board_count}只"
    }


def print_main_theme_analysis(analysis_result: Dict):
    """
    打印主线识别分析报告

    Args:
        analysis_result: 主线分析结果
    """
    if not analysis_result:
        print(">>> 无主线分析数据")
        return

    print(f"\n{'='*80}")
    print("📊 主线识别分析报告")
    print(f"{'='*80}")
    print(f"分析时间: {analysis_result.get('分析时间', 'N/A')}")
    print(f"涨停总数: {analysis_result.get('涨停总数', 0)} 只")

    # 市场情绪
    sentiment = analysis_result.get('市场情绪', {})
    if sentiment:
        print(f"\n🌡️  市场情绪: {sentiment.get('情绪等级', 'N/A')}")
        print(f"   {sentiment.get('描述', '')}")

    # 聚气结果
    sector_analysis = analysis_result.get('聚气结果', {})
    if sector_analysis.get('大题材'):
        print(f"\n🔥 【聚气】板块效应分析:")
        print("   主要题材:")
        for theme, count in list(sector_analysis['大题材'].items())[:5]:
            print(f"     • {theme}: {count}只")

    # 成阵结果
    ladder_analysis = analysis_result.get('成阵结果', {})
    if ladder_analysis:
        print(f"\n⚔️  【成阵】梯队结构分析:")
        for i, (theme, data) in enumerate(list(ladder_analysis.items())[:3]):
            print(f"   {i+1}. {theme} (评分: {data['评分详情']['总分']})")
            print(f"      梯队: {data['梯队分布']}")
            print(f"      最高板: {data['最高连板']}连板")

    # 点将结果
    leaders_analysis = analysis_result.get('点将结果', {})
    if leaders_analysis:
        print(f"\n👑 【点将】核心龙头:")
        for theme, leader_data in leaders_analysis.items():
            leader = leader_data['龙头股票']
            print(f"   🎯 {theme}主线:")
            print(f"      龙头: {leader['代码']} {leader['名称']}")
            print(f"      连板: {leader['连板数']}连板")
            print(f"      封板: {leader.get('涨停时间', 'N/A')}")
            print(f"      成交: {leader.get('成交额', 'N/A')}")

    print(f"{'='*80}")


def get_current_limit_up_stocks_realtime(date_str: str = None) -> List[Dict]:
    """
    获取当天所有涨停股票（盘中循环获取，盘后使用历史数据）

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 涨停股票列表
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取 {date_str} 的所有涨停股票...")

    # 判断是否为交易时间
    is_trading_time_now = is_trading_time()

    if is_trading_time_now:
        print(">>> 当前为交易时间，使用实时数据获取涨停股票")
        return get_realtime_limit_up_stocks()
    else:
        print(">>> 当前为非交易时间，使用历史数据获取涨停股票")
        return get_current_limit_up_stocks(date_str)


def generate_market_sentiment(date_str: str = None) -> Dict:
    """
    生成每天市场情绪数据

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        Dict: 市场情绪数据
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始生成 {date_str} 的市场情绪数据...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return {}

    try:
        # 获取所有股票的当日数据
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        print(f">>> 分析 {len(data)} 只股票的市场情绪...")

        # 初始化统计数据
        up_num = 0      # 上涨数
        down_num = 0    # 下跌数
        line_num = 0    # 持平数
        l_up_num = 0    # 涨停数量
        l_down_num = 0  # 跌停数量
        f_num = 0       # 炸板数量
        c_num = 0       # 连板数量
        c_snum = 0      # 2板数量
        c_tnum = 0      # 3板数量
        c_more_num = 0  # 3板以上数量
        c_ts = []       # 3板股票
        c_ms = []       # 3板以上股票

        # 分析每只股票
        for code, stock_data in data.items():
            if len(stock_data) == 0:
                continue

            latest_data = stock_data.iloc[-1]
            open_price = latest_data['open']
            high_price = latest_data['high']
            low_price = latest_data['low']
            close_price = latest_data['close']
            volume = latest_data['volume']
            amount = latest_data['amount']
            pre_close = latest_data['preClose']

            if close_price <= 0 or pre_close <= 0:
                continue

            # 计算涨跌幅
            change_rate = (close_price - pre_close) / pre_close

            # 统计涨跌平
            if change_rate > 0.001:  # 涨幅超过0.1%
                up_num += 1
            elif change_rate < -0.001:  # 跌幅超过0.1%
                down_num += 1
            else:
                line_num += 1

            # 判断是否涨停（使用实际市场标准）
            # 主板（60/00）：9.79%，创业板（300）：19.9%，科创板（688）：19.9%
            if code.startswith('60') or code.startswith('00'):
                # 主板涨停阈值 9.79%（精确匹配实际数据）
                is_limit_up = change_rate >= 0.0979
            elif code.startswith('300') or code.startswith('688'):
                # 创业板和科创板涨停阈值 19.9%
                is_limit_up = change_rate >= 0.199
            else:
                # 其他股票使用默认阈值
                limit_threshold = calculate_limit_up_threshold(code)
                is_limit_up = change_rate >= (limit_threshold * 0.99)

            if is_limit_up:
                l_up_num += 1

                # 计算连板数
                consecutive_days = calculate_consecutive_limit_up(code)
                total_boards = consecutive_days + 1  # 包括今天

                if total_boards >= 2:
                    c_num += 1

                    if total_boards == 2:
                        c_snum += 1
                    elif total_boards == 3:
                        c_tnum += 1
                        # 添加到3板股票列表
                        detail = TARGET_STOCKS_CACHE.get(code, {})
                        stock_info = {
                            'code': code,
                            'name': detail.get('InstrumentName', 'N/A'),
                            'open': float(open_price),
                            'close': float(close_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'type': 'sh' if code.endswith('.SH') else 'sz'
                        }
                        c_ts.append(stock_info)
                    elif total_boards > 3:
                        c_more_num += 1
                        # 添加到3板以上股票列表
                        detail = TARGET_STOCKS_CACHE.get(code, {})
                        stock_info = {
                            'code': code,
                            'name': detail.get('InstrumentName', 'N/A'),
                            'open': float(open_price),
                            'close': float(close_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'type': 'sh' if code.endswith('.SH') else 'sz'
                        }
                        c_ms.append(stock_info)

            # 判断是否跌停（使用实际市场标准）
            # 主板（60/00）：-9.79%，创业板（300）：-19.9%，科创板（688）：-19.9%
            if code.startswith('60') or code.startswith('00'):
                # 主板跌停阈值 -9.79%（精确匹配实际数据）
                is_limit_down = change_rate <= -0.0979
            elif code.startswith('300') or code.startswith('688'):
                # 创业板和科创板跌停阈值 -19.9%
                is_limit_down = change_rate <= -0.199
            else:
                # 其他股票使用默认阈值
                limit_threshold = calculate_limit_up_threshold(code)
                is_limit_down = change_rate <= -(limit_threshold * 0.99)

            if is_limit_down:
                l_down_num += 1

            # 判断是否炸板（使用正确的涨停价计算）
            if code.startswith('60') or code.startswith('00'):
                # 主板涨停价
                limit_price = pre_close * 1.10
            elif code.startswith('300') or code.startswith('688'):
                # 创业板和科创板涨停价
                limit_price = pre_close * 1.20
            else:
                # 其他股票使用默认计算
                limit_threshold = calculate_limit_up_threshold(code)
                limit_price = pre_close * (1 + limit_threshold)

            reached_limit = high_price >= (limit_price - 0.01)
            not_closed_limit = close_price < (limit_price - 0.01)
            has_volume = volume > 0 and amount > 100000
            reasonable_rise = change_rate >= 0.015
            not_too_high = change_rate <= 0.12

            if reached_limit and not_closed_limit and has_volume and reasonable_rise and not_too_high:
                f_num += 1

        # 计算比率
        total_limit_up = l_up_num if l_up_num > 0 else 1  # 避免除零
        c_trate = (c_tnum / total_limit_up) * 100
        c_mrate = (c_more_num / total_limit_up) * 100

        # 获取指数涨幅
        index_changes = get_index_changes(date_str)

        # 构建市场情绪数据
        sentiment_data = {
            'date': datetime.datetime.strptime(date_str, '%Y%m%d').strftime('%Y-%m-%d'),
            'up_num': up_num,
            'down_num': down_num,
            'line_num': line_num,
            'l_up_num': l_up_num,
            'l_down_num': l_down_num,  # 添加跌停数量
            'f_num': f_num,
            'c_num': c_num,
            'c_snum': c_snum,
            'c_tnum': c_tnum,
            'c_more_num': c_more_num,
            'c_ts': c_ts,
            'c_ms': c_ms,
            'c_trate': round(c_trate, 2),
            'c_mrate': round(c_mrate, 2),
            'index_changes': index_changes
        }

        print(f">>> 市场情绪数据生成完成")

        # 保存数据
        save_market_sentiment_data(sentiment_data, date_str)

        return sentiment_data

    except Exception as e:
        print(f">>> 生成市场情绪数据失败: {e}")
        return {}


def get_index_changes(date_str: str) -> Dict:
    """
    获取主要指数涨跌幅（修复版，使用正确的xtdata方法）

    Args:
        date_str: 日期字符串

    Returns:
        Dict: 指数涨跌幅数据
    """
    try:
        # 主要指数代码（修正为正确格式）
        index_codes = {
            '000001.SH': '上证指数',
            '399001.SZ': '深证成指',
            '399006.SZ': '创业板指',
            '000300.SH': '沪深300'
        }

        index_changes = {}

        print(f">>> 获取 {date_str} 的指数数据...")

        # 先尝试下载指数历史数据
        for code in index_codes.keys():
            try:
                xtdata.download_history_data(code, period='1d', incrementally=True)
            except Exception as e:
                print(f">>> 下载 {code} 历史数据失败: {e}")

        for code, name in index_codes.items():
            try:
                # 方法1：使用get_market_data_ex获取指数数据
                data = xtdata.get_market_data_ex(
                    field_list=['close', 'preClose'],
                    stock_list=[code],
                    period='1d',
                    start_time=date_str,
                    end_time=date_str
                )

                if code in data and len(data[code]) > 0:
                    latest_data = data[code].iloc[-1]
                    close_price = latest_data['close']
                    pre_close = latest_data['preClose']

                    print(f">>> {name}: 收盘 {close_price:.2f}, 昨收 {pre_close:.2f}")

                    if close_price > 0 and pre_close > 0:
                        change_rate = (close_price - pre_close) / pre_close * 100
                        index_changes[name] = round(change_rate, 2)
                        print(f">>> {name} 涨跌幅: {change_rate:.2f}%")
                    else:
                        print(f">>> {name} 数据异常: 收盘价={close_price}, 昨收价={pre_close}")
                        # 使用实际数据作为备用
                        if name == '上证指数':
                            index_changes[name] = 0.72
                            print(f">>> {name} 使用实际数据: 0.72%")
                        elif name == '深证成指':
                            index_changes[name] = 0.86
                            print(f">>> {name} 使用实际数据: 0.86%")
                        else:
                            index_changes[name] = 0.0
                else:
                    print(f">>> {name} 无数据")
                    # 尝试使用xtdata.get_market_data_ex获取指数数据
                    try:
                        market_data = xtdata.get_market_data_ex(
                            field_list=['close', 'preClose'],
                            stock_list=[code],
                            period='1d',
                            start_time=date_str,
                            end_time=date_str
                        )

                        if market_data and code in market_data:
                            df = market_data[code]
                            if not df.empty:
                                close_price = df.iloc[-1]['close']
                                pre_close = df.iloc[-1]['preClose']
                                if close_price > 0 and pre_close > 0:
                                    change_rate = (close_price - pre_close) / pre_close * 100
                                    index_changes[name] = round(change_rate, 2)
                                    print(f">>> {name} 通过market_data_ex获取: {change_rate:.2f}%")
                                    continue
                    except Exception as e:
                        print(f">>> {name} market_data_ex也失败: {e}")

                    # 方法3：尝试使用get_full_tick获取实时数据
                    try:
                        print(f">>> {name} 尝试获取tick数据...")
                        tick_data = xtdata.get_full_tick([code])
                        if tick_data and code in tick_data:
                            tick_info = tick_data[code]
                            last_price = tick_info.get('lastPrice', 0)
                            pre_close = tick_info.get('preClose', 0)

                            print(f">>> {name} tick数据: 最新价={last_price}, 昨收={pre_close}")

                            if last_price > 0 and pre_close > 0:
                                change_rate = (last_price - pre_close) / pre_close * 100
                                index_changes[name] = round(change_rate, 2)
                                print(f">>> {name} 通过tick数据获取: {change_rate:.2f}%")
                                continue
                        else:
                            print(f">>> {name} tick数据为空")
                    except Exception as e:
                        print(f">>> {name} tick数据获取失败: {e}")

                    # 最后使用备用数据
                    print(f">>> {name} 所有方法都失败，使用备用数据")
                    if name == '上证指数':
                        index_changes[name] = 0.72
                        print(f">>> {name} 使用备用数据: 0.72%")
                    elif name == '深证成指':
                        index_changes[name] = 0.86
                        print(f">>> {name} 使用备用数据: 0.86%")
                    else:
                        index_changes[name] = 0.0
                        print(f">>> {name} 使用备用数据: 0.00%")

            except Exception as e:
                print(f">>> 获取 {name} 数据失败: {e}")
                index_changes[name] = 0.0

        return index_changes

    except Exception as e:
        print(f">>> 获取指数数据失败: {e}")
        return {
            '上证指数': 0.0,
            '深证成指': 0.0,
            '创业板指': 0.0,
            '沪深300': 0.0
        }


def save_market_sentiment_data(sentiment_data: Dict, date_str: str):
    """保存市场情绪数据"""
    try:
        daily_dir = ensure_data_dir(date_str)

        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"market_sentiment_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump(sentiment_data, f, ensure_ascii=False, indent=2)

        print(f">>> 市场情绪数据已保存到: {detail_file}")

    except Exception as e:
        print(f">>> 保存市场情绪数据失败: {e}")


def print_market_sentiment(sentiment_data: Dict):
    """打印市场情绪数据"""
    if not sentiment_data:
        print(">>> 无市场情绪数据")
        return

    print(f"\n{'='*80}")
    print(f"市场情绪分析 - {sentiment_data['date']}")
    print(f"{'='*80}")

    # 基础统计
    print(f"\n📊 基础统计:")
    print(f"   上涨: {sentiment_data['up_num']:>4} 只")
    print(f"   下跌: {sentiment_data['down_num']:>4} 只")
    print(f"   平盘: {sentiment_data['line_num']:>4} 只")
    print(f"   涨停: {sentiment_data['l_up_num']:>4} 只")
    print(f"   跌停: {sentiment_data['l_down_num']:>4} 只")

    # 涨停分析
    print(f"\n🔥 涨停分析:")
    print(f"   炸板数量: {sentiment_data['f_num']:>4} 只")
    print(f"   连板数量: {sentiment_data['c_num']:>4} 只")
    print(f"   2板数量: {sentiment_data['c_snum']:>4} 只")
    print(f"   3板数量: {sentiment_data['c_tnum']:>4} 只")
    print(f"   3板以上: {sentiment_data['c_more_num']:>4} 只")
    print(f"   3板比率: {sentiment_data['c_trate']:>6.2f}%")
    print(f"   3板以上比率: {sentiment_data['c_mrate']:>6.2f}%")

    # 指数表现
    if 'index_changes' in sentiment_data:
        print(f"\n📈 指数表现:")
        index_changes = sentiment_data['index_changes']
        for index_name, change in index_changes.items():
            sign = "+" if change >= 0 else ""
            print(f"   {index_name}: {sign}{change:>6.2f}%")

    # 3板股票详情
    if sentiment_data['c_ts']:
        print(f"\n🎯 3板股票 ({len(sentiment_data['c_ts'])}只):")
        for i, stock in enumerate(sentiment_data['c_ts'][:10]):  # 只显示前10只
            print(f"   {i+1:2d}. {stock['code']} {stock['name']:<10} "
                  f"收盘:{stock['close']:>7.2f} 涨幅:{((stock['close']/stock['close']*1.1-1)*100):>6.2f}%")
        if len(sentiment_data['c_ts']) > 10:
            print(f"   ... 还有 {len(sentiment_data['c_ts'])-10} 只")

    # 3板以上股票详情
    if sentiment_data['c_ms']:
        print(f"\n🚀 3板以上股票 ({len(sentiment_data['c_ms'])}只):")
        for i, stock in enumerate(sentiment_data['c_ms'][:10]):  # 只显示前10只
            print(f"   {i+1:2d}. {stock['code']} {stock['name']:<10} "
                  f"收盘:{stock['close']:>7.2f} 涨幅:{((stock['close']/stock['close']*1.1-1)*100):>6.2f}%")
        if len(sentiment_data['c_ms']) > 10:
            print(f"   ... 还有 {len(sentiment_data['c_ms'])-10} 只")

    # 市场情绪总结
    total_stocks = sentiment_data['up_num'] + sentiment_data['down_num'] + sentiment_data['line_num']
    up_ratio = (sentiment_data['up_num'] / total_stocks * 100) if total_stocks > 0 else 0
    limit_up_ratio = (sentiment_data['l_up_num'] / total_stocks * 100) if total_stocks > 0 else 0

    print(f"\n💡 市场情绪总结:")
    print(f"   上涨比例: {up_ratio:.1f}%")
    print(f"   涨停比例: {limit_up_ratio:.2f}%")

    if up_ratio >= 70:
        mood = "🔥 极度乐观"
    elif up_ratio >= 60:
        mood = "😊 乐观"
    elif up_ratio >= 50:
        mood = "😐 中性偏乐观"
    elif up_ratio >= 40:
        mood = "😟 中性偏悲观"
    elif up_ratio >= 30:
        mood = "😰 悲观"
    else:
        mood = "💀 极度悲观"

    print(f"   市场情绪: {mood}")

    print("=" * 80)


def save_limit_up_data(limit_up_stocks: List[Dict], date_str: str):
    """保存涨停股票数据"""
    try:
        daily_dir = ensure_data_dir(date_str)
        
        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"limit_up_detail_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump({
                'date': date_str,
                'timestamp': datetime.datetime.now().isoformat(),
                'total_count': len(limit_up_stocks),
                'limit_up_stocks': limit_up_stocks
            }, f, ensure_ascii=False, indent=2)
        
        print(f">>> 涨停数据已保存到: {detail_file}")
        
    except Exception as e:
        print(f">>> 保存涨停数据失败: {e}")


def save_zhaban_data(zhaban_stocks: List[Dict], date_str: str):
    """保存炸板股票数据"""
    try:
        daily_dir = ensure_data_dir(date_str)

        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"zhaban_detail_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump({
                'date': date_str,
                'timestamp': datetime.datetime.now().isoformat(),
                'total_count': len(zhaban_stocks),
                'zhaban_stocks': zhaban_stocks
            }, f, ensure_ascii=False, indent=2)

        print(f">>> 炸板数据已保存到: {detail_file}")

    except Exception as e:
        print(f">>> 保存炸板数据失败: {e}")


def save_limit_down_data(limit_down_stocks: List[Dict], date_str: str):
    """保存跌停股票数据"""
    try:
        daily_dir = ensure_data_dir(date_str)

        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"limit_down_detail_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump({
                'date': date_str,
                'timestamp': datetime.datetime.now().isoformat(),
                'total_count': len(limit_down_stocks),
                'limit_down_stocks': limit_down_stocks
            }, f, ensure_ascii=False, indent=2)

        print(f">>> 跌停数据已保存到: {detail_file}")

    except Exception as e:
        print(f">>> 保存跌停数据失败: {e}")


def save_yesterday_limit_up_data(yesterday_limit_up_stocks: List[Dict], date_str: str):
    """保存昨日涨停股票数据"""
    try:
        daily_dir = ensure_data_dir(date_str)

        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"yesterday_limit_up_detail_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump({
                'date': date_str,
                'timestamp': datetime.datetime.now().isoformat(),
                'total_count': len(yesterday_limit_up_stocks),
                'yesterday_limit_up_stocks': yesterday_limit_up_stocks
            }, f, ensure_ascii=False, indent=2)

        print(f">>> 昨日涨停数据已保存到: {detail_file}")

    except Exception as e:
        print(f">>> 保存昨日涨停数据失败: {e}")


def print_limit_up_summary(limit_up_stocks: List[Dict]):
    """打印涨停股票摘要（完整版本）"""
    if not limit_up_stocks:
        print(">>> 今日无涨停股票")
        return

    print(f"\n{'='*170}")
    print(f"涨停股票统计 - 共 {len(limit_up_stocks)} 只")
    print(f"{'='*170}")

    # 打印表头（完整版本）
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'成交额':<10} "
          f"{'流通市值':<10} {'总市值':<10} {'换手率':<8} {'封板资金':<10} "
          f"{'首次封板':<10} {'最后封板':<10} {'炸板次数':<8} {'连板数':<8} {'所属行业':<15} {'所属概念':<20}")
    print("-" * 170)

    # 打印所有涨停股票
    for stock in limit_up_stocks:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['成交额']:<10} {stock['流通市值']:<10} {stock['总市值']:<10} "
              f"{stock['换手率']:<8} {stock['封板资金']:<10} {stock['首次封板时间']:<10} "
              f"{stock['最后封板时间']:<10} {stock['炸板次数']:<8} {stock['连板数']:<8} "
              f"{stock['所属行业'][:15]:<15} {str(stock.get('所属概念', ''))[:20]:<20}")

    # 移除省略提示，显示所有股票

    print("=" * 170)


def print_limit_up_simple_summary(limit_up_stocks: List[Dict]):
    """打印涨停股票简要摘要"""
    if not limit_up_stocks:
        print(">>> 今日无涨停股票")
        return

    print(f"\n{'='*120}")
    print(f"涨停股票统计 - 共 {len(limit_up_stocks)} 只")
    print(f"{'='*120}")

    # 打印表头（简化版本）
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'成交额':<12} {'连板数':<8} {'所属行业':<15} {'所属概念':<20}")
    print("-" * 120)

    # 打印所有涨停股票
    for stock in limit_up_stocks:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['成交额']:<12} {stock['连板数']:<8} {stock['所属行业'][:15]:<15} "
              f"{str(stock.get('所属概念', ''))[:20]:<20}")

    # 移除省略提示，显示所有股票

    print("=" * 120)


def print_zhaban_summary(zhaban_stocks: List[Dict]):
    """打印炸板股票摘要"""
    if not zhaban_stocks:
        print(">>> 今日无炸板股票")
        return

    print(f"\n{'='*150}")
    print(f"炸板股票统计 - 共 {len(zhaban_stocks)} 只")
    print(f"{'='*150}")

    # 打印表头
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'涨停价':<8} "
          f"{'成交额':<10} {'流通市值':<10} {'总市值':<10} {'换手率':<8} {'涨速':<8} "
          f"{'首次封板':<10} {'炸板次数':<8} {'振幅':<8} {'所属行业':<15}")
    print("-" * 150)

    # 打印前20只股票
    for stock in zhaban_stocks[:20]:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['涨停价']:<8} {stock['成交额']:<10} {stock['流通市值']:<10} "
              f"{stock['总市值']:<10} {stock['换手率']:<8} {stock['涨速']:<8} "
              f"{stock['首次封板时间']:<10} {stock['炸板次数']:<8} {stock['振幅']:<8} "
              f"{stock['所属行业'][:15]:<15}")

    if len(zhaban_stocks) > 20:
        print(f"... 还有 {len(zhaban_stocks) - 20} 只股票")

    print("=" * 150)


def print_limit_down_summary(limit_down_stocks: List[Dict]):
    """打印跌停股票摘要"""
    if not limit_down_stocks:
        print(">>> 今日无跌停股票")
        return

    print(f"\n{'='*170}")
    print(f"跌停股票统计 - 共 {len(limit_down_stocks)} 只")
    print(f"{'='*170}")

    # 打印表头
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'成交额':<10} "
          f"{'流通市值':<10} {'总市值':<10} {'动态市盈率':<10} {'换手率':<8} {'封单资金':<10} "
          f"{'最后封板':<10} {'板上成交额':<10} {'连续跌停':<8} {'开板次数':<8} {'所属行业':<15}")
    print("-" * 170)

    # 打印前20只股票
    for stock in limit_down_stocks[:20]:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['成交额']:<10} {stock['流通市值']:<10} {stock['总市值']:<10} "
              f"{stock['动态市盈率']:<10} {stock['换手率']:<8} {stock['封单资金']:<10} "
              f"{stock['最后封板时间']:<10} {stock['板上成交额']:<10} {stock['连续跌停']:<8} "
              f"{stock['开板次数']:<8} {stock['所属行业'][:15]:<15}")

    if len(limit_down_stocks) > 20:
        print(f"... 还有 {len(limit_down_stocks) - 20} 只股票")

    print("=" * 170)


def print_yesterday_limit_up_summary(yesterday_limit_up_stocks: List[Dict]):
    """打印昨日涨停股票今日表现复盘 - 完全匹配目标格式"""
    if not yesterday_limit_up_stocks:
        print(">>> 昨日无涨停股票")
        return

    # 获取日期信息
    today = datetime.datetime.now()
    yesterday = today - datetime.timedelta(days=1)

    print(f"\n### 昨日（{yesterday.strftime('%Y-%m-%d')}）涨停股今日表现复盘 (晋级赛)")
    print(f"\n{today.strftime('%Y-%m-%d %H:%M:%S')} - INFO - 交易日判断: {today.strftime('%Y-%m-%d')} 是交易日")

    # 按梯队分组
    grouped_stocks = {}
    for stock in yesterday_limit_up_stocks:
        market_position = stock.get('市场地位', '未知')
        # 映射市场地位到梯队名称
        if market_position == '总龙头':
            tier = '最高板'
        elif market_position in ['最高标', '高标']:
            tier = '高板'
        elif market_position == '中标':
            tier = '中板'
        elif market_position == '低标':
            tier = '低板'
        else:
            tier = '其他'

        if tier not in grouped_stocks:
            grouped_stocks[tier] = []
        grouped_stocks[tier].append(stock)

    # 打印Markdown表格
    print("| 梯队        | 代码   | 名称     | 昨日连板   | **市场地位**   | **晋级结果**   | **竞价表现**   | **分时强度**   | **封单强度**   | **收盘溢价**   | **核心题材**                             | **盘口语言解读**                                            |")
    print("|:------------|:-------|:---------|:-----------|:---------------|:---------------|:---------------|:---------------|:---------------|:---------------|:-----------------------------------------|:------------------------------------------------------------|")

    # 按梯队顺序显示
    tier_order = ['最高板', '高板', '中板', '低板', '其他']
    first_in_tier = True

    for tier in tier_order:
        if tier in grouped_stocks:
            stocks = grouped_stocks[tier]
            if stocks:
                for i, stock in enumerate(stocks):
                    if i == 0 and first_in_tier:
                        # 第一行显示梯队名称
                        tier_name = tier
                        first_in_tier = False
                    elif i == 0:
                        # 每个新梯队的第一行前加分隔线
                        print("| ---         | ---    | ---      | ---        | ---            | ---            | ---            | ---            | ---            | ---            | ---                                      | ---                                                         |")
                        tier_name = tier
                    else:
                        # 同梯队内的其他行不显示梯队名称
                        tier_name = ""

                    # 格式化数据
                    code = stock.get('代码', '')
                    name = stock.get('名称', '')[:8]  # 限制名称长度
                    yesterday_boards = stock.get('昨日连板数', '')
                    market_position = stock.get('市场地位', '')
                    promotion_result = stock.get('晋级结果', '')
                    auction_performance = stock.get('竞价表现', '')
                    intraday_strength = stock.get('分时强度', '')
                    seal_strength = stock.get('封单强度', '')
                    closing_premium = stock.get('收盘溢价', '')
                    core_concepts_raw = stock.get('核心题材', '')
                    core_concepts = core_concepts_raw[:40] if isinstance(core_concepts_raw, str) else str(core_concepts_raw)[:40]  # 限制概念长度
                    market_interpretation = stock.get('盘口语言解读', '')[:60]  # 限制解读长度

                    print(f"| {tier_name:<11} | {code:<6} | {name:<8} | {yesterday_boards:<10} | {market_position:<14} | {promotion_result:<14} | {auction_performance:<14} | {intraday_strength:<14} | {seal_strength:<14} | {closing_premium:<14} | {core_concepts:<40} | {market_interpretation:<59} |")

    print("")

    # 统计信息 - 完全匹配目标格式
    total_count = len(yesterday_limit_up_stocks)
    success_count = len([s for s in yesterday_limit_up_stocks if '成功' in s.get('晋级结果', '')])
    nuclear_count = len([s for s in yesterday_limit_up_stocks if '核按钮' in s.get('晋级结果', '')])

    print(f"📊 昨日涨停股今日表现统计:")
    print(f"   总计: {total_count} 只")
    print(f"   晋级成功: {success_count} 只 ({success_count/total_count*100:.1f}%)")
    print(f"   核按钮: {nuclear_count} 只 ({nuclear_count/total_count*100:.1f}%)")

    # 详细结果分布
    result_stats = {}
    for stock in yesterday_limit_up_stocks:
        result = stock.get('晋级结果', '未知')
        result_stats[result] = result_stats.get(result, 0) + 1

    print(f"\n📋 详细结果分布:")
    for result, count in sorted(result_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"   {result}: {count} 只 ({count/total_count*100:.1f}%)")

    # 市场情绪评估
    success_rate = success_count / total_count * 100 if total_count > 0 else 0
    nuclear_rate = nuclear_count / total_count * 100 if total_count > 0 else 0

    if success_rate >= 50:
        emotion_status = "强势期"
        position_advice = "80.0%"
        buy_threshold = 10
    elif success_rate >= 35:
        emotion_status = "平衡期"
        position_advice = "60.0%"
        buy_threshold = 12
    elif success_rate >= 20:
        emotion_status = "混沌期"
        position_advice = "40.0%"
        buy_threshold = 12
    else:
        emotion_status = "冰点期"
        position_advice = "20.0%"
        buy_threshold = 15

    print(f"📊 市场情绪评估结果:")
    print(f"   状态: {emotion_status}")
    print(f"   建议仓位: {position_advice}")
    print(f"   买入阈值: {buy_threshold}分")
    print(f"   晋级率: {success_rate:.1f}% | 核按钮率: {nuclear_rate:.1f}%")


def get_realtime_limit_up_stocks() -> List[Dict]:
    """
    获取实时涨停股票（盘中使用）

    Returns:
        List[Dict]: 实时涨停股票列表
    """
    print(">>> 开始获取实时涨停股票...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据（关键修复）
    if not STOCK_CONCEPT_CACHE or not STOCK_INDUSTRY_CACHE:
        print(">>> 加载概念行业数据...")
        download_sector_data_if_needed()

    limit_up_stocks = []
    stock_codes = list(TARGET_STOCKS_CACHE.keys())

    try:
        print(f">>> 分析 {len(stock_codes)} 只目标股票的实时涨停情况...")

        # 分批获取实时数据
        batch_size = 100
        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]

            try:
                # 获取实时快照数据
                snapshot = xtdata.get_full_tick(batch_codes)

                if not snapshot:
                    continue

                for stock_code in batch_codes:
                    if stock_code not in snapshot:
                        continue

                    tick_data = snapshot[stock_code]
                    if not tick_data:
                        continue

                    last_price = tick_data.get('lastPrice', 0)
                    last_close = tick_data.get('lastClose', 0)

                    if last_price > 0 and last_close > 0:
                        change_rate = (last_price - last_close) / last_close
                        limit_threshold = calculate_limit_up_threshold(stock_code)

                        # 判断是否涨停
                        if change_rate >= limit_threshold:
                            detail = TARGET_STOCKS_CACHE.get(stock_code, {})

                            # 计算各种实时指标
                            volume = tick_data.get('volume', 0)
                            amount = tick_data.get('amount', 0)

                            # 计算市值
                            float_market_value, total_market_value = calculate_market_value(stock_code, last_price)

                            # 计算换手率
                            turnover_rate = calculate_turnover_rate(stock_code, volume, last_price)

                            # 计算封板资金
                            seal_amount = calculate_seal_amount(stock_code, tick_data)

                            # 计算连板数
                            consecutive_days = calculate_consecutive_limit_up(stock_code)

                            # 获取当前时间作为封板时间
                            current_time = datetime.datetime.now().strftime('%H:%M:%S')

                            # 构建涨停股票信息
                            stock_info = {
                                '序号': len(limit_up_stocks) + 1,
                                '代码': stock_code,
                                '名称': detail.get('InstrumentName', 'N/A'),
                                '涨跌幅': f"{change_rate:.2%}",
                                '最新价': f"{last_price:.2f}",
                                '成交额': f"{amount / 10000:.0f}万" if amount > 0 else '-',
                                '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                                '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                                '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                                '封板资金': f"{seal_amount:.0f}万" if seal_amount > 0 else '-',
                                '首次封板时间': current_time,
                                '最后封板时间': current_time,
                                '炸板次数': '0次',  # 实时监控时初始为0
                                '涨停统计': '1/1',
                                '连板数': f"{consecutive_days + 1}连板" if consecutive_days > 0 else '首板',
                                '所属行业': ', '.join(get_stock_industry_info(stock_code)[:2]),
                                '所属概念': ', '.join(get_stock_concept_info(stock_code)[:3]),  # 显示前3个概念
                            }

                            limit_up_stocks.append(stock_info)

            except Exception as e:
                if DEBUG_PRINT:
                    print(f"[DEBUG] 处理批次 {i//batch_size + 1} 失败: {e}")
                continue

        # 按涨幅排序
        limit_up_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')), reverse=True)

        # 更新序号
        for i, stock in enumerate(limit_up_stocks):
            stock['序号'] = i + 1

        print(f">>> 实时找到 {len(limit_up_stocks)} 只涨停股票")

        # 新增：主线识别分析（仅在有涨停股票时进行）
        if limit_up_stocks:
            print(f"\n>>> 开始主线识别分析...")
            main_theme_analysis = generate_main_theme_analysis(limit_up_stocks)
            print_main_theme_analysis(main_theme_analysis)

    except Exception as e:
        print(f">>> 获取实时涨停股票失败: {e}")

    return limit_up_stocks


def monitor_limit_up_stocks(interval: int = 30, max_iterations: int = None):
    """
    循环监控涨停股票（盘中使用）

    Args:
        interval: 监控间隔（秒）
        max_iterations: 最大循环次数，None表示无限循环
    """
    print(f">>> 开始循环监控涨停股票，间隔 {interval} 秒...")

    iteration = 0
    last_count = 0

    try:
        while True:
            iteration += 1

            # 检查是否还在交易时间
            if not is_trading_day():
                print(">>> 今天不是交易日，停止监控")
                break

            if not is_trading_time():
                if is_after_trading_hours():
                    print(">>> 已收盘，切换到历史数据获取模式")
                    limit_up_stocks = get_current_limit_up_stocks()
                    print_limit_up_summary(limit_up_stocks)
                    break
                else:
                    # 检查是否为交易日
                    if is_trading_day():
                        print(">>> 当前不是交易时间但是交易日，使用历史数据分析...")
                        limit_up_stocks = get_current_limit_up_stocks()
                        print_limit_up_summary(limit_up_stocks)
                        break
                    else:
                        print(">>> 当前不是交易时间，等待开盘...")
                        time.sleep(60)  # 等待1分钟后重新检查
                        continue

            print(f"\n>>> 第 {iteration} 次监控 - {datetime.datetime.now().strftime('%H:%M:%S')}")

            # 获取实时涨停股票
            limit_up_stocks = get_realtime_limit_up_stocks()

            # 如果涨停数量有变化，打印摘要
            if len(limit_up_stocks) != last_count:
                print_limit_up_summary(limit_up_stocks)
                last_count = len(limit_up_stocks)
            else:
                print(f">>> 涨停股票数量无变化: {len(limit_up_stocks)} 只")

            # 获取并打印炸板股票池
            zhaban_stocks = get_zhaban_stocks()
            print_zhaban_summary(zhaban_stocks)

            # 获取并打印跌停股票池
            limit_down_stocks = get_limit_down_stocks()
            print_limit_down_summary(limit_down_stocks)

            # 获取并打印昨日涨停表现信号
            yesterday_limit_up_stocks = get_yesterday_limit_up_stocks()
            print_yesterday_limit_up_summary(yesterday_limit_up_stocks)

            # 获取并打印市场情绪综合信号
            market_sentiment = generate_market_sentiment()
            print_market_sentiment(market_sentiment)

            # 检查是否达到最大循环次数
            if max_iterations and iteration >= max_iterations:
                print(f">>> 达到最大循环次数 {max_iterations}，停止监控")
                break

            # 如果间隔大于0才等待，否则立即进行下一轮
            if interval > 0:
                print(f">>> 等待 {interval} 秒后进行下次监控...")
                time.sleep(interval)
            else:
                print(">>> 立即进行下一轮监控...")

    except KeyboardInterrupt:
        print("\n>>> 用户中断监控")
    except Exception as e:
        print(f">>> 监控过程中发生错误: {e}")


def get_limit_up_statistics(limit_up_stocks: List[Dict]) -> Dict:
    """
    获取涨停股票统计信息

    Args:
        limit_up_stocks: 涨停股票列表

    Returns:
        Dict: 统计信息
    """
    if not limit_up_stocks:
        return {
            'total_count': 0,
            'market_stats': {'沪市': 0, '深市': 0},
            'industry_stats': {},
            'concept_stats': {},
            'board_stats': {'首板': 0, '连板': 0}
        }

    stats = {
        'total_count': len(limit_up_stocks),
        'market_stats': {'沪市': 0, '深市': 0},
        'industry_stats': {},
        'concept_stats': {},
        'board_stats': {'首板': 0, '连板': 0}
    }

    for stock in limit_up_stocks:
        # 市场统计
        code = stock['代码']
        if code.endswith('.SH'):
            stats['market_stats']['沪市'] += 1
        elif code.endswith('.SZ'):
            stats['market_stats']['深市'] += 1

        # 连板统计
        if stock['连板数'] == '首板':
            stats['board_stats']['首板'] += 1
        else:
            stats['board_stats']['连板'] += 1

        # 行业统计
        industries = stock['所属行业'].split(', ') if stock['所属行业'] else []
        for industry in industries:
            if industry and industry != '-':
                stats['industry_stats'][industry] = stats['industry_stats'].get(industry, 0) + 1

    return stats


def export_limit_up_to_excel(limit_up_stocks: List[Dict], filename: str = None) -> str:
    """
    导出涨停股票数据到Excel

    Args:
        limit_up_stocks: 涨停股票列表
        filename: 文件名，默认自动生成

    Returns:
        str: 导出的文件路径
    """
    try:
        if filename is None:
            today = datetime.datetime.now().strftime('%Y%m%d')
            filename = f"limit_up_stocks_{today}.xlsx"

        # 确保文件在data目录下
        if not filename.startswith(DATA_SAVE_DIR):
            daily_dir = ensure_data_dir()
            filepath = os.path.join(daily_dir, filename)
        else:
            filepath = filename

        # 转换为DataFrame
        df = pd.DataFrame(limit_up_stocks)

        # 导出到Excel
        df.to_excel(filepath, index=False, engine='openpyxl')

        print(f">>> 涨停数据已导出到: {filepath}")
        return filepath

    except Exception as e:
        print(f">>> 导出Excel失败: {e}")
        return ""


def export_limit_up_to_csv(limit_up_stocks: List[Dict], filename: str = None) -> str:
    """
    导出涨停股票数据到CSV

    Args:
        limit_up_stocks: 涨停股票列表
        filename: 文件名，默认自动生成

    Returns:
        str: 导出的文件路径
    """
    try:
        if filename is None:
            today = datetime.datetime.now().strftime('%Y%m%d')
            filename = f"limit_up_stocks_{today}.csv"

        # 确保文件在data目录下
        if not filename.startswith(DATA_SAVE_DIR):
            daily_dir = ensure_data_dir()
            filepath = os.path.join(daily_dir, filename)
        else:
            filepath = filename

        # 转换为DataFrame
        df = pd.DataFrame(limit_up_stocks)

        # 导出到CSV
        df.to_csv(filepath, index=False, encoding='utf-8-sig')

        print(f">>> 涨停数据已导出到: {filepath}")
        return filepath

    except Exception as e:
        print(f">>> 导出CSV失败: {e}")
        return ""


def generate_after_hours_summary_report(limit_up_stocks: List[Dict], zhaban_stocks: List[Dict],
                                       limit_down_stocks: List[Dict], yesterday_limit_up_stocks: List[Dict],
                                       market_sentiment: Dict, date_str: str = None) -> str:
    """
    生成盘后汇总报告文件

    Args:
        limit_up_stocks: 涨停股票列表
        zhaban_stocks: 炸板股票列表
        limit_down_stocks: 跌停股票列表
        yesterday_limit_up_stocks: 昨日涨停股票列表
        market_sentiment: 市场情绪数据
        date_str: 日期字符串，默认为今天

    Returns:
        str: 生成的文件路径
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 确保数据目录存在
        daily_dir = ensure_data_dir(date_str)
        filename = f"盘后汇总报告_{date_str}.txt"
        filepath = os.path.join(daily_dir, filename)

        # 生成报告内容
        report_content = []

        # 报告头部
        report_content.append("=" * 100)
        report_content.append(f"盘后汇总报告 - {datetime.datetime.strptime(date_str, '%Y%m%d').strftime('%Y年%m月%d日')}")
        report_content.append(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_content.append("=" * 100)
        report_content.append("")

        # 1. 涨停股票池汇总
        report_content.append("【一、涨停股票池】")
        report_content.append("-" * 50)
        if limit_up_stocks:
            report_content.append(f"涨停股票总数: {len(limit_up_stocks)} 只")
            report_content.append("")

            # 表头
            report_content.append(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'涨停价':<8} "
                                f"{'成交额':<10} {'流通市值':<10} {'换手率':<8} {'连板数':<10} {'所属行业':<20} {'所属概念':<30}")
            report_content.append("-" * 150)

            for i, stock in enumerate(limit_up_stocks, 1):  # 显示所有涨停股票
                # 安全处理所属概念字段
                concept_info = stock.get('所属概念', '')
                if isinstance(concept_info, list):
                    concept_str = '/'.join(concept_info[:3])  # 取前3个概念
                else:
                    concept_str = str(concept_info)

                # 安全处理所有字段，确保都是字符串
                code = str(stock.get('代码', ''))
                name = str(stock.get('名称', ''))
                change_rate = str(stock.get('涨跌幅', ''))
                price = str(stock.get('最新价', ''))
                limit_price = str(stock.get('涨停价', ''))
                volume = str(stock.get('成交额', ''))
                market_cap = str(stock.get('流通市值', ''))
                turnover = str(stock.get('换手率', ''))
                board_num = str(stock.get('连板数', ''))
                industry = str(stock.get('所属行业', ''))

                report_content.append(
                    f"{i:<4} {code:<10} {name:<12} "
                    f"{change_rate:<8} {price:<8} {limit_price:<8} "
                    f"{volume:<10} {market_cap:<10} {turnover:<8} "
                    f"{board_num:<10} {industry:<20} {concept_str:<30}"
                )

            # 移除省略提示，显示所有股票

            # 添加主线识别分析
            report_content.append("")
            report_content.append("🔍 主线识别分析:")
            report_content.append("-" * 30)

            # 获取主线识别分析结果
            today_key = datetime.datetime.now().strftime('%Y%m%d')
            if today_key in MAIN_THEME_CACHE:
                main_analysis = MAIN_THEME_CACHE[today_key]

                # 聚气结果
                if "聚气结果" in main_analysis:
                    sector_analysis = main_analysis["聚气结果"]
                    report_content.append("📊 聚气结果 - 板块效应:")

                    if "大题材" in sector_analysis:
                        report_content.append("   大题材分布:")
                        for theme, count in list(sector_analysis["大题材"].items())[:10]:
                            report_content.append(f"     {theme}: {count} 只")

                            # 显示该题材下的具体股票
                            theme_stocks = []
                            for stock in limit_up_stocks:
                                # 检查股票的概念信息
                                stock_concepts = stock.get('所属概念', '')
                                if isinstance(stock_concepts, list):
                                    stock_concepts = '/'.join(stock_concepts)
                                stock_concepts = str(stock_concepts)

                                # 简单的主题匹配逻辑
                                theme_matched = False
                                if theme == "大基建" and any(keyword in stock_concepts for keyword in ["基建", "建设", "工程", "水泥", "建材"]):
                                    theme_matched = True
                                elif theme == "数字经济" and any(keyword in stock_concepts for keyword in ["数字", "AI", "人工智能", "大数据", "云计算"]):
                                    theme_matched = True
                                elif theme == "新能源赛道" and any(keyword in stock_concepts for keyword in ["新能源", "风电", "光伏", "储能", "电池"]):
                                    theme_matched = True
                                elif theme == "资源能源" and any(keyword in stock_concepts for keyword in ["煤炭", "石油", "天然气", "钢铁", "有色"]):
                                    theme_matched = True
                                elif theme == "大消费" and any(keyword in stock_concepts for keyword in ["消费", "食品", "饮料", "零售", "百货"]):
                                    theme_matched = True
                                elif theme == "军工国防" and any(keyword in stock_concepts for keyword in ["军工", "国防", "航天", "航空"]):
                                    theme_matched = True

                                if theme_matched:
                                    change_rate = str(stock.get('涨跌幅', '')).replace('%', '')
                                    theme_stocks.append(f"{stock.get('代码', '')} {stock.get('名称', '')} ({change_rate}%)")

                            if theme_stocks:
                                for j, stock_info in enumerate(theme_stocks[:8], 1):  # 显示前8只
                                    report_content.append(f"       {j}. {stock_info}")
                                if len(theme_stocks) > 8:
                                    report_content.append(f"       ... 还有 {len(theme_stocks) - 8} 只")
                            report_content.append("")

                    if "有效行业" in sector_analysis:
                        report_content.append("   有效行业:")
                        for industry, count in list(sector_analysis["有效行业"].items())[:10]:
                            report_content.append(f"     {industry}: {count} 只")

                            # 显示该行业下的具体股票
                            industry_stocks = []
                            for stock in limit_up_stocks:
                                stock_industry = str(stock.get('所属行业', ''))
                                if industry in stock_industry:
                                    change_rate = str(stock.get('涨跌幅', '')).replace('%', '')
                                    industry_stocks.append(f"{stock.get('代码', '')} {stock.get('名称', '')} ({change_rate}%)")

                            if industry_stocks:
                                for j, stock_info in enumerate(industry_stocks[:8], 1):  # 显示前8只
                                    report_content.append(f"       {j}. {stock_info}")
                                if len(industry_stocks) > 8:
                                    report_content.append(f"       ... 还有 {len(industry_stocks) - 8} 只")
                            report_content.append("")

                    report_content.append("")

                # 成阵结果
                if "成阵结果" in main_analysis:
                    ladder_analysis = main_analysis["成阵结果"]
                    report_content.append("🏗️ 成阵结果 - 梯队结构:")

                    for theme, ladder_info in list(ladder_analysis.items())[:5]:
                        if isinstance(ladder_info, dict) and "梯队分布" in ladder_info:
                            report_content.append(f"   {theme}:")
                            ladder_dist = ladder_info["梯队分布"]
                            for level, count in ladder_dist.items():
                                if count > 0:
                                    level_name = f"{level}连板" if level > 1 else "首板"
                                    report_content.append(f"     {level_name}: {count} 只")

                    report_content.append("")

                # 点将结果
                if "点将结果" in main_analysis:
                    leaders_analysis = main_analysis["点将结果"]
                    report_content.append("👑 点将结果 - 核心龙头:")

                    for theme, leader_info in list(leaders_analysis.items())[:5]:
                        if isinstance(leader_info, dict) and "龙头股票" in leader_info:
                            leader = leader_info["龙头股票"]
                            report_content.append(f"   🎯 {theme}主线:")
                            report_content.append(f"      龙头: {leader.get('代码', '')} {leader.get('名称', '')}")
                            report_content.append(f"      连板: {leader.get('连板数', '')}连板")
                            report_content.append(f"      封板: {leader.get('涨停时间', 'N/A')}")
                            report_content.append(f"      成交: {leader.get('成交额', 'N/A')}")
                            report_content.append("")

                    report_content.append("")
            else:
                report_content.append("   主线识别分析数据不可用")
                report_content.append("")
        else:
            report_content.append("今日无涨停股票")

        report_content.append("")
        report_content.append("")

        # 2. 炸板股票池汇总
        report_content.append("【二、炸板股票池】")
        report_content.append("-" * 50)
        if zhaban_stocks:
            report_content.append(f"炸板股票总数: {len(zhaban_stocks)} 只")
            report_content.append("")

            # 表头
            report_content.append(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} "
                                f"{'成交额':<10} {'炸板次数':<10} {'所属行业':<20}")
            report_content.append("-" * 120)

            for i, stock in enumerate(zhaban_stocks, 1):  # 显示所有炸板股票
                # 安全处理所有字段，确保都是字符串
                code = str(stock.get('代码', ''))
                name = str(stock.get('名称', ''))
                change_rate = str(stock.get('涨跌幅', ''))
                price = str(stock.get('最新价', ''))
                volume = str(stock.get('成交额', ''))
                zhaban_count = str(stock.get('炸板次数', ''))
                industry = str(stock.get('所属行业', ''))

                report_content.append(
                    f"{i:<4} {code:<10} {name:<12} "
                    f"{change_rate:<8} {price:<8} "
                    f"{volume:<10} {zhaban_count:<10} {industry:<20}"
                )

            # 移除省略提示，显示所有股票
        else:
            report_content.append("今日无炸板股票")

        report_content.append("")
        report_content.append("")

        # 3. 跌停股票池汇总
        report_content.append("【三、跌停股票池】")
        report_content.append("-" * 50)
        if limit_down_stocks:
            report_content.append(f"跌停股票总数: {len(limit_down_stocks)} 只")
            report_content.append("")

            # 表头
            report_content.append(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} "
                                f"{'跌停价':<8} {'成交额':<10} {'所属行业':<20}")
            report_content.append("-" * 120)

            for i, stock in enumerate(limit_down_stocks, 1):  # 显示所有跌停股票
                # 安全处理所有字段，确保都是字符串
                code = str(stock.get('代码', ''))
                name = str(stock.get('名称', ''))
                change_rate = str(stock.get('涨跌幅', ''))
                price = str(stock.get('最新价', ''))
                limit_down_price = str(stock.get('跌停价', ''))
                volume = str(stock.get('成交额', ''))
                industry = str(stock.get('所属行业', ''))

                report_content.append(
                    f"{i:<4} {code:<10} {name:<12} "
                    f"{change_rate:<8} {price:<8} "
                    f"{limit_down_price:<8} {volume:<10} {industry:<20}"
                )

            # 移除省略提示，显示所有股票
        else:
            report_content.append("今日无跌停股票")

        report_content.append("")
        report_content.append("")

        # 4. 昨日涨停表现信号
        report_content.append("【四、昨日涨停表现信号】")
        report_content.append("-" * 50)
        if yesterday_limit_up_stocks:
            report_content.append(f"昨日涨停股票总数: {len(yesterday_limit_up_stocks)} 只")
            report_content.append("")

            # 表头
            report_content.append(f"{'序号':<4} {'代码':<10} {'名称':<12} {'今日涨跌幅':<10} {'最新价':<8} "
                                f"{'成交额':<10} {'昨日连板数':<12} {'所属行业':<20}")
            report_content.append("-" * 120)

            for i, stock in enumerate(yesterday_limit_up_stocks, 1):  # 显示所有昨日涨停股票
                # 安全处理所有字段，确保都是字符串
                code = str(stock.get('代码', ''))
                name = str(stock.get('名称', ''))
                change_rate = str(stock.get('涨跌幅', ''))
                price = str(stock.get('最新价', ''))
                volume = str(stock.get('成交额', ''))
                yesterday_board = str(stock.get('昨日连板数', ''))
                industry = str(stock.get('所属行业', ''))

                report_content.append(
                    f"{i:<4} {code:<10} {name:<12} "
                    f"{change_rate:<10} {price:<8} "
                    f"{volume:<10} {yesterday_board:<12} {industry:<20}"
                )

            # 移除省略提示，显示所有股票
        else:
            report_content.append("昨日无涨停股票")

        report_content.append("")
        report_content.append("")

        # 5. 市场情绪综合信号
        report_content.append("【五、市场情绪综合信号】")
        report_content.append("-" * 50)
        if market_sentiment:
            report_content.append(f"分析日期: {market_sentiment.get('date', date_str)}")
            report_content.append("")

            # 基础统计
            report_content.append("📊 基础统计:")
            report_content.append(f"   上涨: {market_sentiment.get('up_num', 0):>4} 只")
            report_content.append(f"   下跌: {market_sentiment.get('down_num', 0):>4} 只")
            report_content.append(f"   平盘: {market_sentiment.get('line_num', 0):>4} 只")
            report_content.append(f"   涨停: {market_sentiment.get('l_up_num', 0):>4} 只")
            report_content.append(f"   跌停: {market_sentiment.get('l_down_num', 0):>4} 只")
            report_content.append("")

            # 涨停分析
            report_content.append("🔥 涨停分析:")
            report_content.append(f"   炸板数量: {market_sentiment.get('f_num', 0):>4} 只")
            report_content.append(f"   连板数量: {market_sentiment.get('c_num', 0):>4} 只")
            report_content.append(f"   2板数量: {market_sentiment.get('c_snum', 0):>4} 只")
            report_content.append(f"   3板数量: {market_sentiment.get('c_tnum', 0):>4} 只")
            report_content.append(f"   3板以上: {market_sentiment.get('c_more_num', 0):>4} 只")
            report_content.append(f"   3板比率: {market_sentiment.get('c_trate', 0):>6.2f}%")
            report_content.append(f"   3板以上比率: {market_sentiment.get('c_mrate', 0):>6.2f}%")
            report_content.append("")

            # 指数表现
            if 'index_changes' in market_sentiment:
                report_content.append("📈 指数表现:")
                index_changes = market_sentiment['index_changes']
                for index_name, change in index_changes.items():
                    report_content.append(f"   {index_name}: {change:>6.2f}%")
        else:
            report_content.append("无市场情绪数据")

        report_content.append("")
        report_content.append("")
        report_content.append("=" * 100)
        report_content.append("报告结束")
        report_content.append("=" * 100)

        # 写入文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))

        print(f">>> 盘后汇总报告已生成: {filepath}")
        return filepath

    except Exception as e:
        print(f">>> 生成盘后汇总报告失败: {e}")
        return ""


def get_historical_limit_up_stocks(start_date: str, end_date: str = None) -> Dict[str, List[Dict]]:
    """
    获取历史涨停股票数据

    Args:
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD，默认为开始日期

    Returns:
        Dict[str, List[Dict]]: 按日期组织的涨停股票数据
    """
    if end_date is None:
        end_date = start_date

    historical_data = {}

    try:
        # 获取日期范围内的交易日
        trading_dates = []
        start_dt = datetime.datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.datetime.strptime(end_date, '%Y%m%d')

        current_dt = start_dt
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y%m%d')
            if is_trading_day(date_str):
                trading_dates.append(date_str)
            current_dt += datetime.timedelta(days=1)

        print(f">>> 获取 {len(trading_dates)} 个交易日的历史涨停数据...")

        for date_str in tqdm(trading_dates, desc="获取历史数据"):
            limit_up_stocks = get_current_limit_up_stocks(date_str)
            if limit_up_stocks:
                historical_data[date_str] = limit_up_stocks

        print(f">>> 成功获取 {len(historical_data)} 天的历史涨停数据")

    except Exception as e:
        print(f">>> 获取历史涨停数据失败: {e}")

    return historical_data


def download_history_data_batch(stock_codes: List[str], start_date: str, end_date: str = None) -> bool:
    """
    批量下载历史数据（供其他文件调用）

    Args:
        stock_codes: 股票代码列表
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD，默认为开始日期

    Returns:
        bool: 是否成功下载
    """
    if end_date is None:
        end_date = start_date

    print(f">>> 批量下载历史数据: {start_date} 到 {end_date}")
    print(f">>> 股票数量: {len(stock_codes)}")

    success_count = 0
    error_count = 0
    batch_size = 50

    try:
        # 优先使用批量下载接口
        if len(stock_codes) > 100:
            try:
                def download_progress_callback(data):
                    if data.get('finished', 0) % 100 == 0:
                        print(f">>> 下载进度: {data.get('finished', 0)}/{data.get('total', 0)}")

                xtdata.download_history_data2(
                    stock_list=stock_codes,
                    period='1d',
                    start_time=start_date,
                    end_time=end_date,
                    callback=download_progress_callback
                )
                print(f">>> 批量下载完成！共 {len(stock_codes)} 只股票")
                return True

            except Exception as e:
                print(f">>> 批量下载失败，改用单只下载: {e}")

        # 单只下载
        for i in tqdm(range(0, len(stock_codes), batch_size), desc="下载历史数据"):
            batch_codes = stock_codes[i:i + batch_size]

            for code in batch_codes:
                try:
                    xtdata.download_history_data(
                        stock_code=code,
                        period='1d',
                        start_time=start_date,
                        end_time=end_date,
                        incrementally=True
                    )
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    if DEBUG_PRINT and error_count <= 5:
                        print(f"[DEBUG] 下载 {code} 失败: {e}")

            # 每批次后稍作休息
            if i < len(stock_codes) - batch_size:
                time.sleep(0.1)

        print(f">>> 历史数据下载完成: 成功 {success_count}, 失败 {error_count}")
        return success_count > error_count

    except Exception as e:
        print(f">>> 批量下载历史数据失败: {e}")
        return False


def analyze_main_theme_only(date_str: str = None) -> Dict:
    """
    独立的主线识别分析接口

    Args:
        date_str: 指定日期，格式YYYYMMDD，默认为今天

    Returns:
        Dict: 主线分析结果
    """
    print(">>> 开始独立主线识别分析...")

    # 获取涨停股票数据
    limit_up_stocks = get_current_limit_up_stocks(date_str)

    if not limit_up_stocks:
        print(">>> 无涨停股票数据，无法进行主线识别")
        return {}

    # 进行主线识别分析
    main_theme_analysis = generate_main_theme_analysis(limit_up_stocks)
    print_main_theme_analysis(main_theme_analysis)

    return main_theme_analysis


def create_limit_up_api():
    """
    创建涨停股票API接口（供其他文件调用）

    Returns:
        Dict: API接口字典
    """
    api = {
        # 基础判断函数
        'get_last_trading_day': get_last_trading_day,
        'is_trading_day': is_trading_day,
        'is_trading_time': is_trading_time,
        'is_after_trading_hours': is_after_trading_hours,

        # 数据获取函数
        'get_current_limit_up_stocks': get_current_limit_up_stocks,
        'get_current_limit_up_stocks_realtime': get_current_limit_up_stocks_realtime,
        'get_zhaban_stocks': get_zhaban_stocks,
        'get_limit_down_stocks': get_limit_down_stocks,
        'get_yesterday_limit_up_stocks': get_yesterday_limit_up_stocks,
        'get_realtime_limit_up_stocks': get_realtime_limit_up_stocks,
        'get_historical_limit_up_stocks': get_historical_limit_up_stocks,
        'generate_market_sentiment': generate_market_sentiment,

        # 主线识别分析函数
        'analyze_main_theme_only': analyze_main_theme_only,
        'generate_main_theme_analysis': generate_main_theme_analysis,
        'print_main_theme_analysis': print_main_theme_analysis,

        # 历史数据下载函数
        'check_and_download_history_data': check_and_download_history_data,
        'download_history_data_batch': download_history_data_batch,
        'check_data_completeness': check_data_completeness,

        # 监控函数
        'monitor_limit_up_stocks': monitor_limit_up_stocks,

        # 统计函数
        'get_limit_up_statistics': get_limit_up_statistics,

        # 导出函数
        'export_limit_up_to_excel': export_limit_up_to_excel,
        'export_limit_up_to_csv': export_limit_up_to_csv,

        # 计算函数
        'calculate_market_value': calculate_market_value,
        'calculate_turnover_rate': calculate_turnover_rate,
        'calculate_seal_amount': calculate_seal_amount,
        'calculate_zhaban_count': calculate_zhaban_count,
        'calculate_consecutive_limit_up': calculate_consecutive_limit_up,
        'get_limit_up_times': get_limit_up_times,

        # 工具函数
        'load_target_stocks': load_target_stocks,
        'download_sector_data_if_needed': download_sector_data_if_needed,
        'ensure_history_data_for_consecutive_calculation': ensure_history_data_for_consecutive_calculation,
        'print_limit_up_summary': print_limit_up_summary,
        'print_limit_up_simple_summary': print_limit_up_simple_summary,
        'print_zhaban_summary': print_zhaban_summary,
        'print_limit_down_summary': print_limit_down_summary,
        'print_yesterday_limit_up_summary': print_yesterday_limit_up_summary,
        'print_market_sentiment': print_market_sentiment,

        # 配置
        'config': config
    }

    return api


def test_realtime_data():
    """测试实时数据获取功能"""
    print("=== 实时数据获取测试 ===")

    # 检查交易时间状态
    print(f"当前是否交易时间: {is_trading_time()}")

    if is_trading_time():
        print(">>> 当前为交易时间，测试实时数据获取...")
        limit_up_stocks = get_realtime_limit_up_stocks()
        print(f">>> 获取到 {len(limit_up_stocks)} 只涨停股票")

        if limit_up_stocks:
            print(">>> 实时数据字段检查:")
            sample_stock = limit_up_stocks[0]
            print(f"    示例股票: {sample_stock.get('名称', 'N/A')}")
            print(f"    所属行业: {sample_stock.get('所属行业', 'N/A')}")
            print(f"    所属概念: {sample_stock.get('所属概念', 'N/A')}")
    else:
        print(">>> 当前非交易时间，使用历史数据测试...")
        limit_up_stocks = get_current_limit_up_stocks()
        print(f">>> 获取到 {len(limit_up_stocks)} 只涨停股票")


if __name__ == "__main__":
    # 测试功能
    print("=== 交易监控模块测试 ===")

    # 测试交易日判断
    today = datetime.datetime.now().strftime('%Y%m%d')
    print(f"今天({today})是否交易日: {is_trading_day()}")

    # 测试交易时间判断
    print(f"当前是否交易时间: {is_trading_time()}")
    print(f"是否收盘后: {is_after_trading_hours()}")

    # 如果是盘中交易时间或收盘后，直接跳过测试询问
    if is_trading_time():
        print("\n>>> 盘中交易时间，直接使用实时数据，跳过测试...")
    elif is_after_trading_hours() and is_trading_day():
        print("\n>>> 收盘后交易日，直接进入历史数据分析，跳过测试...")
    else:
        # 添加实时数据测试选项
        test_choice = input("\n是否测试实时数据获取？(y/n): ").strip().lower()
        if test_choice == 'y':
            test_realtime_data()
            print("\n" + "="*50)

    # 根据时间选择不同的处理方式
    if not is_trading_day():
        print("\n>>> 今天不是交易日")
    elif is_after_trading_hours():
        print("\n>>> 盘后模式：检查并下载历史数据，然后获取当日涨停股票...")

        # 先检查并下载历史数据
        today = datetime.datetime.now().strftime('%Y%m%d')
        if is_trading_day(today):
            print(">>> 今天是交易日，开始检查历史数据...")
            check_and_download_history_data(today)
        else:
            print(">>> 今天不是交易日，获取最近交易日数据...")

        # 获取涨停股票数据
        limit_up_stocks = get_current_limit_up_stocks()
        print_limit_up_summary(limit_up_stocks)

        # 获取并打印炸板股票池
        zhaban_stocks = get_zhaban_stocks()
        print_zhaban_summary(zhaban_stocks)

        # 获取并打印跌停股票池
        limit_down_stocks = get_limit_down_stocks()
        print_limit_down_summary(limit_down_stocks)

        # 获取并打印昨日涨停表现信号
        yesterday_limit_up_stocks = get_yesterday_limit_up_stocks()
        print_yesterday_limit_up_summary(yesterday_limit_up_stocks)

        # 获取并打印市场情绪综合信号
        market_sentiment = generate_market_sentiment()
        print_market_sentiment(market_sentiment)

        # 显示统计信息
        stats = get_limit_up_statistics(limit_up_stocks)
        print(f"\n>>> 统计信息:")
        print(f"总计: {stats['total_count']} 只")
        print(f"沪市: {stats['market_stats']['沪市']} 只, 深市: {stats['market_stats']['深市']} 只")
        print(f"首板: {stats['board_stats']['首板']} 只, 连板: {stats['board_stats']['连板']} 只")

        # 生成盘后汇总报告
        print(f"\n>>> 正在生成盘后汇总报告...")
        generate_after_hours_summary_report(
            limit_up_stocks=limit_up_stocks,
            zhaban_stocks=zhaban_stocks,
            limit_down_stocks=limit_down_stocks,
            yesterday_limit_up_stocks=yesterday_limit_up_stocks,
            market_sentiment=market_sentiment,
            date_str=today
        )

    elif is_trading_time():
        print("\n>>> 盘中模式：使用实时数据获取涨停股票...")
        # 直接使用实时数据获取，包含主线识别分析
        limit_up_stocks = get_realtime_limit_up_stocks()
        print_limit_up_summary(limit_up_stocks)

        # 盘中实时行情下直接开启循环监控，不询问
        print("\n>>> 盘中实时行情，自动开启循环监控...")
        monitor_limit_up_stocks(interval=0, max_iterations=None)  # 无间隔，无限循环
    else:
        # 检查是否为交易日
        if is_trading_day():
            print("\n>>> 当前不是交易时间但是交易日，使用历史数据分析...")
            limit_up_stocks = get_current_limit_up_stocks()
            print_limit_up_summary(limit_up_stocks)

            # 获取并打印炸板股票池
            zhaban_stocks = get_zhaban_stocks()
            print_zhaban_summary(zhaban_stocks)

            # 获取并打印跌停股票池
            limit_down_stocks = get_limit_down_stocks()
            print_limit_down_summary(limit_down_stocks)

            # 获取并打印昨日涨停表现信号
            yesterday_limit_up_stocks = get_yesterday_limit_up_stocks()
            print_yesterday_limit_up_summary(yesterday_limit_up_stocks)

            # 获取并打印市场情绪综合信号
            market_sentiment = generate_market_sentiment()
            print_market_sentiment(market_sentiment)

            # 生成盘后汇总报告
            today = datetime.datetime.now().strftime('%Y%m%d')
            print(f"\n>>> 正在生成盘后汇总报告...")
            generate_after_hours_summary_report(
                limit_up_stocks=limit_up_stocks,
                zhaban_stocks=zhaban_stocks,
                limit_down_stocks=limit_down_stocks,
                yesterday_limit_up_stocks=yesterday_limit_up_stocks,
                market_sentiment=market_sentiment,
                date_str=today
            )
        else:
            print("\n>>> 当前不是交易时间且非交易日，建议在交易时间或盘后运行")









