#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易监控模块
功能：
1. 判断是否交易日
2. 判断交易时段
3. 获取涨停股票数据
4. 下载概念行业数据
5. 提供可调用的接口

作者：基于test_limit_up.py重构
日期：2025-01-21
"""

import os
import time
import datetime
import json
from typing import List, Dict, Optional, Tuple
from xtquant import xtdata
import pandas as pd
from tqdm import tqdm

# 配置参数
DEBUG_PRINT = False  # 是否打印调试信息
TARGET_STOCK_PREFIXES = ('60', '00', '002', '300', '301', '688')  # 目标股票前缀
DATA_SAVE_DIR = "data"  # 数据保存目录

# 全局缓存
TARGET_STOCKS_CACHE = {}  # 股票基础信息缓存
STOCK_INDUSTRY_CACHE = {}  # 行业信息缓存
STOCK_CONCEPT_CACHE = {}   # 概念信息缓存


class TradingMonitorConfig:
    """交易监控配置类"""

    def __init__(self):
        self.debug_print = DEBUG_PRINT
        self.target_prefixes = TARGET_STOCK_PREFIXES
        self.data_dir = DATA_SAVE_DIR
        self.monitor_interval = 30  # 监控间隔（秒）
        self.max_display_stocks = 20  # 最大显示股票数量
        self.enable_sound_alert = False  # 是否启用声音提醒
        self.alert_threshold = 50  # 涨停数量提醒阈值

    def load_from_file(self, config_file: str = "trading_config.json"):
        """从文件加载配置"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                for key, value in config_data.items():
                    if hasattr(self, key):
                        setattr(self, key, value)

                print(f">>> 配置已从 {config_file} 加载")
            else:
                print(f">>> 配置文件 {config_file} 不存在，使用默认配置")
        except Exception as e:
            print(f">>> 加载配置失败: {e}")

    def save_to_file(self, config_file: str = "trading_config.json"):
        """保存配置到文件"""
        try:
            config_data = {
                'debug_print': self.debug_print,
                'target_prefixes': self.target_prefixes,
                'data_dir': self.data_dir,
                'monitor_interval': self.monitor_interval,
                'max_display_stocks': self.max_display_stocks,
                'enable_sound_alert': self.enable_sound_alert,
                'alert_threshold': self.alert_threshold
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            print(f">>> 配置已保存到 {config_file}")
        except Exception as e:
            print(f">>> 保存配置失败: {e}")


# 全局配置实例
config = TradingMonitorConfig()


def ensure_data_dir(date_str: str = None) -> str:
    """确保数据目录存在"""
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')
    
    daily_dir = os.path.join(DATA_SAVE_DIR, date_str)
    if not os.path.exists(daily_dir):
        os.makedirs(daily_dir)
    return daily_dir


def get_last_trading_day(base_date: str = None) -> str:
    """
    获取上一个交易日

    Args:
        base_date: 基准日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        str: 上一个交易日的日期字符串
    """
    if base_date is None:
        current_date = datetime.datetime.now()
    else:
        current_date = datetime.datetime.strptime(base_date, '%Y%m%d')

    # 向前查找交易日，最多查找10天
    for i in range(1, 11):
        check_date = current_date - datetime.timedelta(days=i)
        check_date_str = check_date.strftime('%Y%m%d')

        if is_trading_day(check_date_str):
            return check_date_str

    # 如果找不到，返回昨天
    yesterday = current_date - datetime.timedelta(days=1)
    return yesterday.strftime('%Y%m%d')


def is_trading_day(date_str: str = None) -> bool:
    """
    判断是否为交易日
    
    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天
        
    Returns:
        bool: True表示是交易日，False表示不是
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取交易日历
        try:
            trading_calendar = xtdata.get_trading_dates(market='SH', start_time=date_str, end_time=date_str)
            if trading_calendar:
                # 将时间戳转换为日期字符串进行比较
                for timestamp in trading_calendar:
                    if timestamp > 1000000000000:  # 毫秒时间戳
                        dt = datetime.datetime.fromtimestamp(timestamp / 1000)
                    else:  # 秒时间戳
                        dt = datetime.datetime.fromtimestamp(timestamp)
                    if dt.strftime('%Y%m%d') == date_str:
                        return True
                return False
            else:
                # 如果获取失败，使用简单的工作日判断
                date_obj = datetime.datetime.strptime(date_str, '%Y%m%d')
                return date_obj.weekday() < 5  # 周一到周五
        except Exception as e:
            if DEBUG_PRINT:
                print(f"[DEBUG] 获取交易日历失败: {e}")
            # 如果获取失败，使用简单的工作日判断
            date_obj = datetime.datetime.strptime(date_str, '%Y%m%d')
            return date_obj.weekday() < 5  # 周一到周五
            
    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 判断交易日失败: {e}")
        return False


def is_trading_time(stock_code: str = '600000.SH') -> bool:
    """
    判断当前是否为交易时间
    
    Args:
        stock_code: 股票代码，用于获取交易时段
        
    Returns:
        bool: True表示是交易时间，False表示不是
    """
    try:
        # 获取交易时段
        trading_times = xtdata.get_trading_time(stock_code)
        if not trading_times:
            # 如果获取失败，使用默认交易时间
            return is_default_trading_time()

        now = datetime.datetime.now()
        current_seconds = now.hour * 3600 + now.minute * 60 + now.second

        for time_segment in trading_times:
            start_time, end_time, trade_type = time_segment
            # 只考虑连续交易时段 (trade_type == 3)
            if trade_type == 3 and start_time <= current_seconds <= end_time:
                return True

        return False
    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 获取交易时段失败: {e}")
        # 如果获取失败，使用默认交易时间判断
        return is_default_trading_time()


def is_default_trading_time() -> bool:
    """默认交易时间判断（9:30-11:30, 13:00-15:00）"""
    now = datetime.datetime.now()
    current_time = now.strftime('%H%M')

    # 交易时间：9:30-11:30, 13:00-15:00
    morning_start, morning_end = '0930', '1130'
    afternoon_start, afternoon_end = '1300', '1500'

    return (morning_start <= current_time <= morning_end) or (afternoon_start <= current_time <= afternoon_end)


def is_after_trading_hours() -> bool:
    """判断是否为收盘后时间（15:00之后）"""
    now = datetime.datetime.now()
    current_time = now.strftime('%H%M')
    return current_time >= '1500'


def load_target_stocks() -> Dict[str, Dict]:
    """加载目标股票列表（参考test_limit_up.py的实现）"""
    global TARGET_STOCKS_CACHE

    if TARGET_STOCKS_CACHE:
        return TARGET_STOCKS_CACHE

    try:
        print(">>> 正在加载股票基础信息...")

        # 步骤1: 获取所有股票，包含北交所
        print(f">>> 步骤1/3: 筛选代码前缀为 {TARGET_STOCK_PREFIXES} 的股票...")

        # 尝试获取更全面的股票列表
        try:
            all_stocks = xtdata.get_stock_list_in_sector('沪深A股')
            print(f">>> 获取到沪深A股 {len(all_stocks)} 只")

            # 尝试获取北交所股票
            try:
                bse_stocks = xtdata.get_stock_list_in_sector('北交所')
                if bse_stocks:
                    all_stocks.extend(bse_stocks)
                    print(f">>> 获取到北交所股票 {len(bse_stocks)} 只")
            except:
                print(">>> 无法获取北交所股票列表，跳过")

        except:
            # 备用方案：获取所有股票
            all_stocks = xtdata.get_stock_list_in_sector('全部A股')
            if not all_stocks:
                all_stocks = xtdata.get_stock_list_in_sector('A股')

        if not all_stocks:
            print(">>> 无法获取股票列表")
            return {}

        print(f">>> 总共获取到 {len(all_stocks)} 只股票")

        # 根据前缀筛选目标股票
        target_stock_list = []
        for code in all_stocks:
            stock_code = code.split('.')[0]
            # 检查是否匹配目标前缀
            for prefix in TARGET_STOCK_PREFIXES:
                if prefix == '8':  # 北交所股票特殊处理
                    if stock_code.startswith('8'):
                        target_stock_list.append(code)
                        break
                else:  # 其他股票按原逻辑
                    if stock_code.startswith(prefix):
                        target_stock_list.append(code)
                        break

        print(f">>> 筛选出 {len(target_stock_list)} 只目标股票")

        # 步骤2: 过滤ST股并缓存详情
        print(">>> 步骤2/3: 过滤ST股并缓存股票详情...")
        for code in tqdm(target_stock_list, desc="加载股票详情"):
            try:
                detail = xtdata.get_instrument_detail(code)
                if not detail:
                    continue

                # 加上类型判断，确保是股票
                try:
                    instrument_type = xtdata.get_instrument_type(code)
                    is_stock = instrument_type.get('stock', False) if instrument_type else True
                except:
                    is_stock = True  # 如果获取类型失败，默认认为是股票

                instrument_name = detail.get('InstrumentName', '')

                # 过滤ST股票、退市股票和其他特殊标记股票
                if (is_stock and
                    'ST' not in instrument_name and
                    '*' not in instrument_name and
                    '退市' not in instrument_name):
                    TARGET_STOCKS_CACHE[code] = detail

            except Exception as e:
                if DEBUG_PRINT:
                    print(f"[DEBUG] 处理 {code} 失败: {e}")
                continue

        print(f">>> 步骤3/3: 成功缓存 {len(TARGET_STOCKS_CACHE)} 只目标股票")
        return TARGET_STOCKS_CACHE

    except Exception as e:
        print(f">>> 加载股票基础信息失败: {e}")
        return {}


def download_sector_data_if_needed() -> bool:
    """检查并下载概念行业数据（参考test_limit_up.py的完善实现）"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    try:
        print(">>> 开始加载板块分类信息...")

        # 检查本地板块数据
        existing_sectors = xtdata.get_sector_list()
        sector_count = len(existing_sectors)
        print(f">>> [发现] 本地已有 {sector_count} 个板块")

        if sector_count >= 50:
            print(">>> [使用] 本地板块数据充足，开始处理...")
            process_sector_data_standard(existing_sectors)
            return True
        else:
            print(">>> [下载] 本地板块数据不足，开始下载...")

        # 下载板块数据
        print(">>> [下载] 开始下载板块数据...")
        xtdata.download_sector_data()

        # 重新获取板块列表
        all_sectors = xtdata.get_sector_list()
        print(f">>> [完成] 下载完成，现在有 {len(all_sectors)} 个板块")
        process_sector_data_standard(all_sectors)

        return True

    except Exception as e:
        print(f">>> [失败] 下载失败: {e}")
        use_fallback_strategy_enhanced()
        return False


def process_sector_data_standard(all_sectors):
    """使用XtQuant标准板块名称处理数据（参考test_limit_up.py）"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    print(">>> [处理] 开始处理标准板块数据...")

    # XtQuant标准行业板块名称
    standard_industry_sectors = [
        '申万一级行业', '申万二级行业', '申万三级行业',
        '证监会行业', '证监会行业板块指数',
        'Wind行业', 'SW行业', '行业板块',
        '银行', '保险', '证券', '房地产', '建筑装饰', '钢铁', '有色金属',
        '煤炭', '石油石化', '化工', '建筑材料', '机械设备', '电气设备',
        '国防军工', '汽车', '家用电器', '纺织服装', '轻工制造', '医药生物',
        '商业贸易', '休闲服务', '农林牧渔', '食品饮料', '计算机', '电子',
        '通信', '传媒', '公用事业', '交通运输', '非银金融', '综合'
    ]

    # XtQuant标准概念板块名称
    standard_concept_sectors = [
        '概念板块', '主题概念', '热点概念', 'TGN概念',
        '新能源', '人工智能', '5G', '新基建', '军工', '芯片', '半导体',
        '新材料', '生物医药', '创新药', '疫苗', '医疗器械', '互联网',
        '物联网', '大数据', '云计算', '区块链', '新零售', '消费电子',
        '智能制造', '工业4.0', '新能源汽车', '锂电池', '光伏', '风电',
        '环保', '节能', '数字经济', '元宇宙', '碳中和', '氢能源',
        '储能', '充电桩', '智能驾驶', '无人驾驶'
    ]

    # 精确匹配标准行业板块
    industry_sectors = []
    for sector in all_sectors:
        # 行业板块匹配
        if any(keyword in sector for keyword in standard_industry_sectors):
            industry_sectors.append(sector)

    # 精确匹配标准概念板块
    concept_sectors = []
    for sector in all_sectors:
        # 概念板块匹配
        if any(keyword in sector for keyword in standard_concept_sectors):
            concept_sectors.append(sector)
        # 或者包含'TGN'前缀的概念板块
        elif sector.startswith('TGN'):
            concept_sectors.append(sector)

    print(f">>> [匹配] 标准行业板块: {len(industry_sectors)} 个")
    print(f">>> [匹配] 标准概念板块: {len(concept_sectors)} 个")

    # 显示前几个作为示例
    if industry_sectors:
        print(f">>> [示例] 行业板块: {industry_sectors[:3]}")
    if concept_sectors:
        print(f">>> [示例] 概念板块: {concept_sectors[:3]}")

    # 建立行业映射
    print(">>> [映射] 开始建立股票-行业映射...")
    build_sector_mapping_optimized(industry_sectors, STOCK_INDUSTRY_CACHE, "行业", 2)

    # 建立概念映射
    print(">>> [映射] 开始建立股票-概念映射...")
    build_sector_mapping_optimized(concept_sectors, STOCK_CONCEPT_CACHE, "概念", 3)

    # 补充未分类股票
    supplement_uncategorized_stocks()

    # 最终统计
    industry_coverage = len(STOCK_INDUSTRY_CACHE) / len(TARGET_STOCKS_CACHE) if TARGET_STOCKS_CACHE else 0
    concept_coverage = len(STOCK_CONCEPT_CACHE) / len(TARGET_STOCKS_CACHE) if TARGET_STOCKS_CACHE else 0
    print(f">>> [完成] 行业覆盖率: {industry_coverage:.1%} ({len(STOCK_INDUSTRY_CACHE)}只)")
    print(f">>> [完成] 概念覆盖率: {concept_coverage:.1%} ({len(STOCK_CONCEPT_CACHE)}只)")


def build_sector_mapping_optimized(sectors, cache_dict, sector_type, max_per_stock):
    """优化的板块映射建立（提高成功率）"""
    if not sectors:
        print(f">>> [警告] 没有可用的{sector_type}板块数据")
        return

    success_count = 0
    error_count = 0

    # 优先处理最重要的板块
    priority_sectors = []
    normal_sectors = []

    for sector in sectors:
        # 优先级判断 - 标准板块优先
        if any(priority_keyword in sector for priority_keyword in [
            '申万一级', '申万二级', '证监会行业', 'TGN',
            '新能源', '人工智能', '5G', '军工', '医药', '芯片'
        ]):
            priority_sectors.append(sector)
        else:
            normal_sectors.append(sector)

    # 处理高优先级板块
    sorted_sectors = priority_sectors + normal_sectors
    max_process = min(30, len(sorted_sectors))  # 增加处理数量
    sectors_to_process = sorted_sectors[:max_process]

    print(f">>> [处理] 将处理 {len(sectors_to_process)} 个{sector_type}板块")

    for i, sector in enumerate(sectors_to_process):
        try:
            print(f">>> [进度] {sector_type} {i + 1}/{len(sectors_to_process)}: {sector[:30]}...", end="")

            stocks_in_sector = xtdata.get_stock_list_in_sector(sector)
            stock_count = 0

            for stock_code in stocks_in_sector:
                if stock_code in TARGET_STOCKS_CACHE:
                    if stock_code not in cache_dict:
                        cache_dict[stock_code] = []

                    # 简化板块名称
                    simplified_name = simplify_sector_name_enhanced(sector)
                    if simplified_name and len(cache_dict[stock_code]) < max_per_stock:
                        cache_dict[stock_code].append(simplified_name)
                        stock_count += 1

            print(f" ✓ 成功: {stock_count} 只股票")
            success_count += 1

        except Exception as e:
            error_count += 1
            print(f" ✗ 失败: {e}")

    print(f">>> [完成] {sector_type}映射处理完成！成功: {success_count}, 失败: {error_count}")


def simplify_sector_name_enhanced(sector_name):
    """增强的板块名称简化（处理THY1等前缀）"""
    import re
    simplified = sector_name

    # 移除常见板块类型前缀
    prefixes_to_remove = [
        '申万一级', '申万二级', '申万三级', '证监会行业', 'Wind行业',
        '行业板块', '概念板块', '主题概念', '热点概念', 'TGN'
    ]

    for prefix in prefixes_to_remove:
        if simplified.startswith(prefix):
            simplified = simplified[len(prefix):].strip()

    # 使用正则表达式移除数据源特定的字母数字前缀，如 "THY1"
    simplified = re.sub(r'^[A-Z0-9]+\s*', '', simplified).strip()

    # 清理多余的标点符号
    simplified = simplified.strip('- _()（）')

    # 如果简化后名称为空或过短，则返回原始名称以保证有内容显示
    if len(simplified) <= 1:
        return sector_name

    return simplified


def supplement_uncategorized_stocks():
    """补充未分类的股票"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    # 为未分类的股票添加基础分类
    for stock_code in TARGET_STOCKS_CACHE:
        # 行业分类补充
        if stock_code not in STOCK_INDUSTRY_CACHE or not STOCK_INDUSTRY_CACHE[stock_code]:
            code_prefix = stock_code[:3]
            if code_prefix.startswith('60'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['沪市主板']
            elif code_prefix.startswith('00'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['深市主板']
            elif code_prefix == '002':
                STOCK_INDUSTRY_CACHE[stock_code] = ['深市中小板']
            elif code_prefix in ('300', '301'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['创业板']
            elif code_prefix.startswith('68'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['科创板']
            elif code_prefix.startswith('8'):
                STOCK_INDUSTRY_CACHE[stock_code] = ['北交所']
            else:
                STOCK_INDUSTRY_CACHE[stock_code] = ['其他']

        # 概念分类补充 - 只添加有意义的概念
        if stock_code not in STOCK_CONCEPT_CACHE or not STOCK_CONCEPT_CACHE[stock_code]:
            # 不添加基础概念，留空等待真正的概念数据
            STOCK_CONCEPT_CACHE[stock_code] = []

    print(f">>> [补充] 确保所有股票都有行业分类")


def use_fallback_strategy_enhanced():
    """增强的备用策略"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    print(">>> [增强备用策略] 开始执行...")

    # 检查是否有任何可用的本地板块数据
    try:
        existing_sectors = xtdata.get_sector_list()
        if len(existing_sectors) > 5:  # 降低门槛
            print(f">>> [策略1] 使用可用的本地数据：{len(existing_sectors)} 个板块")
            process_sector_data_enhanced(existing_sectors)
            return
    except Exception as e:
        print(f">>> [策略1] 失败: {e}")

    # 直接使用智能分类策略
    print(">>> [策略2] 使用纯智能分类策略...")
    supplement_with_intelligent_classification()

    print(">>> [策略2] 智能分类完成，实现100%覆盖")


def process_sector_data_enhanced(all_sectors):
    """增强的板块数据处理（改进版）"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    print(">>> [分析] 开始分析和分类板块...")

    # 扩展的行业和概念关键词
    industry_keywords = [
        # 申万/证监会行业
        '申万一级', '申万二级', '申万三级', '证监会行业', 'Wind行业', 'SW行业',
        '行业板块', '行业指数', '行业分类',
        # 具体行业名称（大幅扩展）
        '银行', '保险', '证券', '房地产', '建筑', '钢铁', '有色', '煤炭',
        '石油', '化工', '石化', '建材', '机械', '电气', '国防', '汽车',
        '家电', '纺织', '轻工', '医药', '商贸', '餐饮', '农业', '林业',
        '食品', '饮料', '计算机', '电子', '通信', '传媒', '公用事业',
        '交通运输', '非银金融', '综合', '休闲服务', '商业贸易'
    ]

    concept_keywords = [
        # 概念主题
        '概念', '主题', '题材', 'TGN', '热点',
        # 热门概念（大幅扩展）
        '新能源', '人工智能', '5G', '新基建', '军工', '芯片', '半导体',
        '新材料', '生物医药', '创新药', '疫苗', '医疗器械', '互联网',
        '物联网', '大数据', '云计算', '区块链', '新零售', '消费电子',
        '智能制造', '工业4.0', '新能源汽车', '锂电池', '光伏', '风电',
        '环保', '节能', '数字经济', '元宇宙', '碳中和', '氢能源',
        '储能', '充电桩', '智能驾驶', '无人驾驶', 'VR', 'AR',
        '网络安全', '信息安全', '工业互联网', '边缘计算'
    ]

    # 改进的板块分类逻辑
    industry_sectors = []
    concept_sectors = []

    for sector in all_sectors:
        # 过滤掉非股票相关板块
        if any(keyword in sector for keyword in ['期货', '期权', 'ETF基金', '可转债', '债券']):
            continue

        # 行业板块匹配（扩大匹配范围）
        if any(keyword in sector for keyword in industry_keywords):
            industry_sectors.append(sector)
        # 概念板块匹配
        elif any(keyword in sector for keyword in concept_keywords):
            concept_sectors.append(sector)

    print(f">>> [分类] 行业板块: {len(industry_sectors)} 个")
    print(f">>> [分类] 概念板块: {len(concept_sectors)} 个")

    # 显示前几个作为示例
    if industry_sectors:
        print(f">>> [示例] 行业板块: {industry_sectors[:3]}")
    if concept_sectors:
        print(f">>> [示例] 概念板块: {concept_sectors[:3]}")

    # 建立行业映射
    print(">>> [映射] 开始建立股票-行业映射...")
    build_sector_mapping_optimized(industry_sectors, STOCK_INDUSTRY_CACHE, "行业", 2)

    # 建立概念映射
    print(">>> [映射] 开始建立股票-概念映射...")
    build_sector_mapping_optimized(concept_sectors, STOCK_CONCEPT_CACHE, "概念", 3)

    # 使用智能补充策略确保100%覆盖
    supplement_with_intelligent_classification()

    # 最终统计
    industry_coverage = len(STOCK_INDUSTRY_CACHE) / len(TARGET_STOCKS_CACHE) if TARGET_STOCKS_CACHE else 0
    concept_coverage = len(STOCK_CONCEPT_CACHE) / len(TARGET_STOCKS_CACHE) if TARGET_STOCKS_CACHE else 0
    print(f">>> [最终] 行业覆盖率: {industry_coverage:.1%} ({len(STOCK_INDUSTRY_CACHE)}只)")
    print(f">>> [最终] 概念覆盖率: {concept_coverage:.1%} ({len(STOCK_CONCEPT_CACHE)}只)")


def supplement_with_intelligent_classification():
    """智能补充分类，确保100%覆盖率"""
    global STOCK_INDUSTRY_CACHE, STOCK_CONCEPT_CACHE

    print(">>> [智能补充] 开始智能分类补充...")

    # 扩展的智能分类映射
    intelligent_industry_mapping = {
        # 更细致的前缀映射
        '600': '沪市主板-传统产业',
        '601': '沪市主板-金融业',  # 601多为银行
        '603': '沪市主板-制造业',  # 603多为制造业
        '605': '沪市主板-新兴产业',  # 605多为新兴行业
        '000': '深市主板-综合类',
        '001': '深市主板-创新类',
        '002': '深市中小板',
        '300': '创业板-高科技',
        '301': '创业板-新经济',
        '688': '科创板-硬科技',
        '689': '科创板-生物医药',
        '8': '北交所-专精特新'
    }

    # 只为真正没有概念的股票添加有意义的概念
    intelligent_concept_mapping = {
        '688': ['硬科技', '科技创新', '国产替代'],
        '689': ['生物医药', '医疗健康', '创新药'],
        '8': ['专精特新', '小巨人企业']
    }

    supplemented_industry = 0
    supplemented_concept = 0

    for stock_code in TARGET_STOCKS_CACHE:
        # 行业分类补充
        if stock_code not in STOCK_INDUSTRY_CACHE or not STOCK_INDUSTRY_CACHE[stock_code]:
            # 尝试不同长度的前缀匹配
            classified = False
            for prefix_len in [3, 1]:  # 先3位前缀，再1位前缀
                prefix = stock_code[:prefix_len]
                if prefix in intelligent_industry_mapping:
                    STOCK_INDUSTRY_CACHE[stock_code] = [intelligent_industry_mapping[prefix]]
                    supplemented_industry += 1
                    classified = True
                    break

            # 默认行业分类
            if not classified:
                if stock_code.endswith('.SH'):
                    STOCK_INDUSTRY_CACHE[stock_code] = ['沪市股票']
                else:
                    STOCK_INDUSTRY_CACHE[stock_code] = ['深市股票']
                supplemented_industry += 1

        # 概念分类补充 - 只为特定板块添加有意义的概念
        if stock_code not in STOCK_CONCEPT_CACHE or not STOCK_CONCEPT_CACHE[stock_code]:
            classified = False
            for prefix_len in [3, 1]:
                prefix = stock_code[:prefix_len]
                if prefix in intelligent_concept_mapping:
                    STOCK_CONCEPT_CACHE[stock_code] = intelligent_concept_mapping[prefix]
                    supplemented_concept += 1
                    classified = True
                    break

            # 对于其他股票，不添加无意义的概念，保持空列表
            if not classified:
                STOCK_CONCEPT_CACHE[stock_code] = []

    print(f">>> [智能补充完成] 行业: +{supplemented_industry}只, 概念: +{supplemented_concept}只")


def get_stock_industry_info(stock_code: str) -> List[str]:
    """获取股票行业信息（使用缓存，过滤无意义信息）"""
    global STOCK_INDUSTRY_CACHE

    if stock_code in STOCK_INDUSTRY_CACHE:
        industries = STOCK_INDUSTRY_CACHE[stock_code]
        # 过滤掉无意义的基础分类
        meaningless_keywords = {
            '其他', '未分类', '综合行业', '沪市主板', '深市主板', '深市中小板',
            '创业板', '科创板', '北交所', '沪市股票', '深市股票', 'A股市场',
            '沪市主板-传统产业', '沪市主板-金融业', '沪市主板-制造业', '沪市主板-新兴产业',
            '深市主板-综合类', '深市主板-创新类', '创业板-高科技', '创业板-新经济',
            '科创板-硬科技', '科创板-生物医药', '北交所-专精特新'
        }
        filtered_industries = [ind for ind in industries if ind not in meaningless_keywords]
        return filtered_industries if filtered_industries else []

    # 如果缓存中没有，返回空列表（不返回无意义的基础分类）
    return []


def get_stock_concept_info(stock_code: str) -> List[str]:
    """获取股票概念信息（使用缓存，过滤无意义信息）"""
    global STOCK_CONCEPT_CACHE

    if stock_code in STOCK_CONCEPT_CACHE:
        concepts = STOCK_CONCEPT_CACHE[stock_code]
        # 过滤掉无意义的基础分类
        meaningless_keywords = {
            '其他', '未分类', '综合行业', 'A股市场', '沪市股票', '深市股票',
            '中小板股票', '创业板股票', '科创板股票', '北交所股票',
            '沪市主板', '深市主板', '深市中小板', '创业板', '科创板', '北交所'
        }
        filtered_concepts = [con for con in concepts if con not in meaningless_keywords]
        return filtered_concepts if filtered_concepts else []

    # 如果缓存中没有，返回空列表（不返回无意义的基础分类）
    return []


def calculate_limit_up_threshold(stock_code: str) -> float:
    """计算涨停阈值（参考test_limit_up.py的实时监控标准）"""
    code_prefix = stock_code[:3]
    if code_prefix in ('300', '301'):  # 创业板
        return 0.195  # 19.5% (实时监控标准)
    elif code_prefix in ('688', '689'):  # 科创板
        return 0.195  # 19.5% (实时监控标准)
    elif code_prefix.startswith('8'):  # 北交所
        return 0.295  # 29.5%
    else:  # 主板、中小板
        return 0.095  # 9.5% (实时监控标准)


def calculate_market_value(stock_code: str, price: float) -> Tuple[float, float]:
    """
    计算流通市值和总市值（参考xtdata文档）

    Args:
        stock_code: 股票代码
        price: 当前价格

    Returns:
        Tuple[float, float]: (流通市值(亿), 总市值(亿))
    """
    try:
        # 获取股本数据
        detail = xtdata.get_instrument_detail(stock_code)
        if not detail:
            return 0.0, 0.0

        # 获取总股本和流通股本（单位：股）- 使用正确的字段名
        total_shares = detail.get('TotalVolume', 0)  # 总股本
        float_shares = detail.get('FloatVolume', 0)  # 流通股本

        if total_shares <= 0:
            return 0.0, 0.0

        # 计算市值（亿元）
        total_market_value = (total_shares * price) / 100000000  # 总市值
        float_market_value = (float_shares * price) / 100000000 if float_shares > 0 else total_market_value  # 流通市值

        return float_market_value, total_market_value

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 市值失败: {e}")
        return 0.0, 0.0


def calculate_turnover_rate(stock_code: str, volume: float, price: float) -> float:
    """
    计算换手率（参考xtdata文档）

    Args:
        stock_code: 股票代码
        volume: 成交量（股）
        price: 当前价格（暂未使用，保持接口一致性）

    Returns:
        float: 换手率（%）
    """
    try:
        # 获取流通股本
        detail = xtdata.get_instrument_detail(stock_code)
        if not detail:
            return 0.0

        float_shares = detail.get('FloatVolume', 0)  # 流通股本 - 使用正确的字段名

        if float_shares <= 0 or volume <= 0:
            return 0.0

        # 计算换手率 = 成交量 / 流通股本 * 100%
        turnover_rate = (volume / float_shares) * 100

        return turnover_rate

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 换手率失败: {e}")
        return 0.0


def calculate_seal_amount(stock_code: str, tick_data: Dict = None) -> float:
    """
    计算封板资金 - 通过实时盘口数据获取买一档封单金额

    Args:
        stock_code: 股票代码
        tick_data: 实时tick数据

    Returns:
        float: 封板资金（万元）
    """
    try:
        if not tick_data:
            # 获取实时盘口数据
            snapshot = xtdata.get_full_tick([stock_code])
            if stock_code not in snapshot or not snapshot[stock_code]:
                return 0.0
            tick_data = snapshot[stock_code]

        # 获取当前价格和昨收价，判断是否涨停
        last_price = tick_data.get('lastPrice', 0)
        pre_close = tick_data.get('lastClose', 0)

        if last_price <= 0 or pre_close <= 0:
            return 0.0

        # 计算涨停价
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_price = pre_close * (1 + limit_threshold)

        # 判断是否接近涨停价（允许0.01的误差）
        if abs(last_price - limit_price) > 0.01:
            return 0.0  # 不是涨停状态，没有封板资金

        # 获取买一价和买一量
        bid_prices = tick_data.get('bidPrice', [])
        bid_volumes = tick_data.get('bidVol', [])

        if not bid_prices or not bid_volumes or len(bid_prices) == 0 or len(bid_volumes) == 0:
            return 0.0

        bid_price = bid_prices[0]  # 买一价
        bid_volume = bid_volumes[0]  # 买一量

        if bid_price <= 0 or bid_volume <= 0:
            return 0.0

        # 验证买一价是否为涨停价
        if abs(bid_price - limit_price) > 0.01:
            return 0.0  # 买一价不是涨停价，不算封板

        # 计算封板资金 = 买一价 * 买一量 / 10000（万元）
        seal_amount = (bid_price * bid_volume) / 10000

        return seal_amount

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 封板资金失败: {e}")
        return 0.0


def calculate_zhaban_count(stock_code: str, date_str: str = None) -> int:
    """
    计算炸板次数 - 通过分时数据分析涨停板的封板和开板情况（优化版）

    Args:
        stock_code: 股票代码
        date_str: 日期字符串，默认为今天

    Returns:
        int: 炸板次数
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0

        df = data[stock_code]
        if len(df) == 0:
            return 0

        # 获取昨收价
        pre_close = df.iloc[0]['preClose']
        if pre_close <= 0:
            return 0

        # 根据股票类型确定涨停价格
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_price = pre_close * (1 + limit_threshold)

        zhaban_count = 0
        was_sealed = False  # 记录是否曾经封板
        consecutive_sealed_minutes = 0  # 连续封板分钟数

        # 分析每分钟的封板状态
        for _, row in df.iterrows():
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']
            volume = row['volume']
            amount = row.get('amount', 0)

            # 严格的涨停封板判断：
            # 1. 收盘价必须达到涨停价（允许0.01误差）
            # 2. 最低价也要达到涨停价（说明整个分钟都在涨停价上）
            # 3. 有成交量（说明有交易活动）
            is_sealed_now = (
                close_price >= (limit_price - 0.01) and
                low_price >= (limit_price - 0.01) and  # 关键：最低价也要在涨停价
                volume > 0
            )

            # 判断是否触及涨停但未封板（最高价达到涨停价但收盘价或最低价未达到）
            touched_but_not_sealed = (
                high_price >= (limit_price - 0.01) and
                not is_sealed_now
            )

            if is_sealed_now:
                if not was_sealed:
                    # 新的封板开始
                    was_sealed = True
                    consecutive_sealed_minutes = 1
                else:
                    # 继续封板
                    consecutive_sealed_minutes += 1
            elif touched_but_not_sealed or (was_sealed and high_price < (limit_price - 0.02)):
                # 情况1：触及涨停但未封板
                # 情况2：曾经封板但现在明显远离涨停价
                if was_sealed and consecutive_sealed_minutes >= 1:
                    # 曾经封板至少1分钟，现在开板了，算作一次炸板
                    zhaban_count += 1
                was_sealed = False
                consecutive_sealed_minutes = 0

        return zhaban_count

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 炸板次数失败: {e}")
        return 0


def calculate_zhaban_count_simple(stock_code: str, date_str: str, limit_price: float) -> int:
    """
    简化的炸板次数计算（更接近东方财富标准）
    只要触及涨停价后回落就算炸板，不要求严格的封板过程

    Args:
        stock_code: 股票代码
        date_str: 日期字符串
        limit_price: 涨停价

    Returns:
        int: 炸板次数
    """
    try:
        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0  # 无分时数据时返回0，不能假设炸板

        df = data[stock_code]
        if len(df) == 0:
            return 0

        zhaban_count = 0
        touched_limit_before = False

        # 分析每分钟的价格变化
        for _, row in df.iterrows():
            high_price = row['high']
            close_price = row['close']
            volume = row['volume']

            # 判断是否触及涨停价
            touched_limit_now = high_price >= (limit_price - 0.01) and volume > 0

            # 判断是否从涨停价回落
            fell_from_limit = (
                touched_limit_now and
                close_price < (limit_price - 0.02)  # 收盘价明显低于涨停价
            )

            if touched_limit_now:
                touched_limit_before = True

            if fell_from_limit:
                zhaban_count += 1

        # 返回实际计算出的炸板次数，如果从未开板则为0
        return zhaban_count

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 简化计算 {stock_code} 炸板次数失败: {e}")
        return 0  # 异常时返回0，不能假设炸板


def calculate_consecutive_limit_up(stock_code: str) -> int:
    """
    计算连板数 - 下载前10天交易数据进行准确计算

    Args:
        stock_code: 股票代码

    Returns:
        int: 连板数（不包括今天）
    """
    try:
        # 计算开始日期（前15天，确保包含10个交易日）
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=15)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        # 先尝试下载历史数据，确保数据完整
        try:
            xtdata.download_history_data(
                stock_code=stock_code,
                period='1d',
                start_time=start_date_str,
                end_time=end_date_str
            )
        except Exception as download_error:
            if DEBUG_PRINT:
                print(f"[DEBUG] 下载 {stock_code} 历史数据失败: {download_error}")

        # 获取历史数据
        data = xtdata.get_local_data(
            field_list=['close', 'preClose'],
            stock_list=[stock_code],
            period='1d',
            start_time=start_date_str,
            end_time=end_date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0

        df = data[stock_code].sort_index(ascending=False)  # 按时间倒序

        if len(df) < 2:
            return 0

        consecutive_days = 0
        limit_threshold = calculate_limit_up_threshold(stock_code)

        # 从昨天开始往前计算（跳过今天）
        for i in range(1, len(df)):  # 从第二行开始（跳过今天）
            row = df.iloc[i]
            close_price = row['close']
            pre_close = row['preClose']

            if close_price > 0 and pre_close > 0:
                change_rate = (close_price - pre_close) / pre_close

                if change_rate >= limit_threshold:
                    consecutive_days += 1
                else:
                    break  # 连续涨停中断
            else:
                break

        return consecutive_days

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 连板数失败: {e}")
        return 0


def get_limit_up_times(stock_code: str, date_str: str = None) -> Tuple[str, str]:
    """
    获取首次封板时间和最后封板时间 - 通过分时数据精确分析

    Args:
        stock_code: 股票代码
        date_str: 日期字符串，默认为今天

    Returns:
        Tuple[str, str]: (首次封板时间, 最后封板时间)
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return "09:25:00", "09:25:00"

        df = data[stock_code]
        if len(df) == 0:
            return "09:25:00", "09:25:00"

        # 获取昨收价和涨停价
        pre_close = df.iloc[0]['preClose']
        if pre_close <= 0:
            return "09:25:00", "09:25:00"

        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_price = pre_close * (1 + limit_threshold)

        first_seal_time = None
        last_seal_time = None

        # 分析每分钟的封板状态
        for idx, row in df.iterrows():
            close_price = row['close']
            high_price = row['high']
            volume = row['volume']

            # 判断是否封板（收盘价达到涨停价且有成交量）
            is_sealed = (close_price >= (limit_price - 0.01)) and volume > 0

            # 或者最高价触及涨停价也算封板尝试
            touched_limit = high_price >= (limit_price - 0.01)

            if is_sealed or touched_limit:
                # 获取时间字符串
                if hasattr(idx, 'strftime'):
                    time_str = idx.strftime('%H:%M:%S')
                elif hasattr(idx, 'time'):
                    time_str = idx.time().strftime('%H:%M:%S')
                else:
                    # 从索引名称中提取时间
                    time_str = str(idx)[-8:] if len(str(idx)) >= 8 else "09:25:00"
                    if ':' not in time_str:
                        time_str = "09:25:00"

                if first_seal_time is None:
                    first_seal_time = time_str

                # 只有真正封板的才更新最后封板时间
                if is_sealed:
                    last_seal_time = time_str

        return (first_seal_time or "09:25:00", last_seal_time or first_seal_time or "09:25:00")

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 获取 {stock_code} 封板时间失败: {e}")
        return "09:25:00", "09:25:00"


def calculate_limit_down_seal_amount(stock_code: str, tick_data: Dict = None) -> float:
    """
    计算跌停封单资金 - 通过实时盘口数据获取卖一档封单金额

    Args:
        stock_code: 股票代码
        tick_data: 实时tick数据

    Returns:
        float: 封单资金（万元）
    """
    try:
        if not tick_data:
            # 获取实时盘口数据
            snapshot = xtdata.get_full_tick([stock_code])
            if stock_code not in snapshot or not snapshot[stock_code]:
                return 0.0
            tick_data = snapshot[stock_code]

        # 获取当前价格和昨收价，判断是否跌停
        last_price = tick_data.get('lastPrice', 0)
        pre_close = tick_data.get('lastClose', 0)

        if last_price <= 0 or pre_close <= 0:
            return 0.0

        # 计算跌停价
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_down_price = pre_close * (1 - limit_threshold)

        # 判断是否接近跌停价（允许0.01的误差）
        if abs(last_price - limit_down_price) > 0.01:
            return 0.0  # 不是跌停状态，没有封单资金

        # 获取卖一价和卖一量
        ask_prices = tick_data.get('askPrice', [])
        ask_volumes = tick_data.get('askVol', [])

        if not ask_prices or not ask_volumes or len(ask_prices) == 0 or len(ask_volumes) == 0:
            return 0.0

        ask_price = ask_prices[0]  # 卖一价
        ask_volume = ask_volumes[0]  # 卖一量

        if ask_price <= 0 or ask_volume <= 0:
            return 0.0

        # 验证卖一价是否为跌停价
        if abs(ask_price - limit_down_price) > 0.01:
            return 0.0  # 卖一价不是跌停价，不算封板

        # 计算封单资金 = 卖一价 * 卖一量 / 10000（万元）
        seal_amount = (ask_price * ask_volume) / 10000

        return seal_amount

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 跌停封单资金失败: {e}")
        return 0.0


def get_limit_down_times(stock_code: str, date_str: str = None) -> str:
    """
    获取跌停封板时间

    Args:
        stock_code: 股票代码
        date_str: 日期字符串，默认为今天

    Returns:
        str: 最后封板时间
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return "09:25:00"

        df = data[stock_code]
        if len(df) == 0:
            return "09:25:00"

        # 获取昨收价和跌停价
        pre_close = df.iloc[0]['preClose']
        if pre_close <= 0:
            return "09:25:00"

        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_down_price = pre_close * (1 - limit_threshold)

        last_seal_time = None

        # 分析每分钟的跌停状态
        for idx, row in df.iterrows():
            close_price = row['close']
            volume = row['volume']

            # 判断是否跌停（收盘价达到跌停价且有成交量）
            is_limit_down = (close_price <= (limit_down_price + 0.01)) and volume > 0

            if is_limit_down:
                # 获取时间字符串
                if hasattr(idx, 'strftime'):
                    time_str = idx.strftime('%H:%M:%S')
                elif hasattr(idx, 'time'):
                    time_str = idx.time().strftime('%H:%M:%S')
                else:
                    # 从索引名称中提取时间
                    time_str = str(idx)[-8:] if len(str(idx)) >= 8 else "09:25:00"
                    if ':' not in time_str:
                        time_str = "09:25:00"

                last_seal_time = time_str

        return last_seal_time or "09:25:00"

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 获取 {stock_code} 跌停时间失败: {e}")
        return "09:25:00"


def calculate_consecutive_limit_down(stock_code: str) -> int:
    """
    计算连续跌停天数

    Args:
        stock_code: 股票代码

    Returns:
        int: 连续跌停天数（不包括今天）
    """
    try:
        # 计算开始日期（前15天，确保包含10个交易日）
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=15)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        # 获取历史数据
        data = xtdata.get_local_data(
            field_list=['close', 'preClose'],
            stock_list=[stock_code],
            period='1d',
            start_time=start_date_str,
            end_time=end_date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0

        df = data[stock_code].sort_index(ascending=False)  # 按时间倒序

        if len(df) < 2:
            return 0

        consecutive_days = 0
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_down_threshold = -limit_threshold  # 跌停阈值为负值

        # 从昨天开始往前计算（跳过今天）
        for i in range(1, len(df)):  # 从第二行开始（跳过今天）
            row = df.iloc[i]
            close_price = row['close']
            pre_close = row['preClose']

            if close_price > 0 and pre_close > 0:
                change_rate = (close_price - pre_close) / pre_close

                if change_rate <= limit_down_threshold:
                    consecutive_days += 1
                else:
                    break  # 连续跌停中断
            else:
                break

        return consecutive_days

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 连续跌停天数失败: {e}")
        return 0


def calculate_limit_down_open_count(stock_code: str, date_str: str = None) -> int:
    """
    计算跌停开板次数

    Args:
        stock_code: 股票代码
        date_str: 日期字符串，默认为今天

    Returns:
        int: 开板次数
    """
    try:
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y%m%d')

        # 获取分时数据（1分钟K线）
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'preClose', 'volume'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        if stock_code not in data or len(data[stock_code]) == 0:
            return 0

        df = data[stock_code]
        if len(df) == 0:
            return 0

        # 获取昨收价
        pre_close = df.iloc[0]['preClose']
        if pre_close <= 0:
            return 0

        # 根据股票类型确定跌停价格
        limit_threshold = calculate_limit_up_threshold(stock_code)
        limit_down_price = pre_close * (1 - limit_threshold)

        open_count = 0
        was_limit_down = False  # 记录是否曾经跌停

        # 分析每分钟的跌停状态
        for _, row in df.iterrows():
            low_price = row['low']
            close_price = row['close']
            volume = row['volume']

            # 判断当前分钟是否跌停（收盘价达到跌停价且有成交量）
            is_limit_down_now = (close_price <= (limit_down_price + 0.01)) and volume > 0

            # 判断是否触及跌停（最低价达到跌停价）
            touched_limit_down = low_price <= (limit_down_price + 0.01)

            if touched_limit_down:
                if not is_limit_down_now and was_limit_down:
                    # 曾经跌停但现在开板了，算作一次开板
                    open_count += 1
                    was_limit_down = False
                elif is_limit_down_now:
                    was_limit_down = True

        return open_count

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 计算 {stock_code} 跌停开板次数失败: {e}")
        return 0


def ensure_history_data_for_consecutive_calculation(stock_codes: List[str]) -> None:
    """
    确保连板计算所需的历史数据完整性

    Args:
        stock_codes: 股票代码列表
    """
    try:
        print(">>> 检查连板计算所需的历史数据...")

        # 计算需要的日期范围（前15天）
        end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=15)
        start_date_str = start_date.strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')

        # 批量下载历史数据
        batch_size = 100
        total_batches = (len(stock_codes) + batch_size - 1) // batch_size

        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            batch_num = i // batch_size + 1

            print(f">>> 下载历史数据批次 {batch_num}/{total_batches} ({len(batch_codes)} 只股票)...")

            try:
                xtdata.download_history_data(
                    stock_code=batch_codes,
                    period='1d',
                    start_time=start_date_str,
                    end_time=end_date_str
                )
            except Exception as e:
                if DEBUG_PRINT:
                    print(f"[DEBUG] 批次 {batch_num} 下载失败: {e}")
                continue

        print(">>> 历史数据检查完成")

    except Exception as e:
        if DEBUG_PRINT:
            print(f"[DEBUG] 确保历史数据失败: {e}")


def check_and_download_history_data(date_str: str = None) -> bool:
    """
    检查并下载历史数据（盘后使用）

    Args:
        date_str: 指定日期，格式YYYYMMDD，默认为今天

    Returns:
        bool: 是否成功下载数据
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 检查 {date_str} 的历史数据...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return False

    stock_codes = list(TARGET_STOCKS_CACHE.keys())

    # 检查数据完整性
    need_download_codes = check_data_completeness(stock_codes, date_str)

    if not need_download_codes:
        print(f">>> {date_str} 的数据已完整，无需下载")
        return True

    print(f">>> 需要下载 {len(need_download_codes)} 只股票的历史数据")

    # 下载历史数据
    success_count = 0
    error_count = 0
    batch_size = 50

    for i in tqdm(range(0, len(need_download_codes), batch_size), desc="下载历史数据"):
        batch_codes = need_download_codes[i:i + batch_size]

        for code in batch_codes:
            try:
                xtdata.download_history_data(
                    stock_code=code,
                    period='1d',
                    start_time=date_str,
                    end_time=date_str,
                    incrementally=True
                )
                success_count += 1
            except Exception as e:
                error_count += 1
                if DEBUG_PRINT and error_count <= 5:
                    print(f"[DEBUG] 下载 {code} 失败: {e}")

        # 每批次后稍作休息
        if i < len(need_download_codes) - batch_size:
            time.sleep(0.1)

    print(f">>> 历史数据下载完成: 成功 {success_count}, 失败 {error_count}")
    return success_count > 0


def check_data_completeness(stock_codes: List[str], date_str: str) -> List[str]:
    """
    检查数据完整性，返回需要下载的股票代码列表

    Args:
        stock_codes: 股票代码列表
        date_str: 检查的日期

    Returns:
        List[str]: 需要下载的股票代码列表
    """
    need_download = []

    # 随机抽样检查，提高效率
    import random
    sample_size = min(50, len(stock_codes))
    sample_codes = random.sample(stock_codes, sample_size)

    missing_count = 0
    for code in sample_codes:
        try:
            data = xtdata.get_local_data(
                field_list=['close'],
                stock_list=[code],
                period='1d',
                start_time=date_str,
                end_time=date_str,
                dividend_type='none'
            )

            if code not in data or len(data[code]) == 0:
                missing_count += 1
        except Exception:
            missing_count += 1

    # 如果超过30%的样本缺少数据，则需要下载所有股票
    missing_ratio = missing_count / sample_size
    if DEBUG_PRINT:
        print(f"[DEBUG] 数据完整性检查: {missing_count}/{sample_size} 缺失 ({missing_ratio:.1%})")

    if missing_ratio > 0.3:
        print(f">>> 检测到 {missing_ratio:.1%} 的数据缺失，需要下载历史数据")
        return stock_codes
    else:
        print(">>> 历史数据基本完整")
        return []


def get_current_limit_up_stocks(date_str: str = None) -> List[Dict]:
    """
    获取当天所有涨停股票

    Args:
        date_str: 指定日期，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 涨停股票列表，包含所有需要的字段
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取 {date_str} 的涨停股票...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据
    download_sector_data_if_needed()

    # 确保连板计算所需的历史数据
    stock_codes = list(TARGET_STOCKS_CACHE.keys())
    if stock_codes:
        ensure_history_data_for_consecutive_calculation(stock_codes[:200])  # 限制数量避免过长时间

    # 如果是盘后且是交易日，先检查并下载历史数据
    if is_after_trading_hours() and is_trading_day(date_str):
        print(">>> 检测到盘后时间，检查历史数据...")
        check_and_download_history_data(date_str)

    limit_up_stocks = []
    stock_codes = list(TARGET_STOCKS_CACHE.keys())

    print(f">>> 分析 {len(stock_codes)} 只股票的涨停情况...")

    # 分批处理，提高效率
    batch_size = 100
    for i in range(0, len(stock_codes), batch_size):
        batch_codes = stock_codes[i:i + batch_size]

        try:
            # 获取当日数据
            data = xtdata.get_local_data(
                field_list=['close', 'preClose', 'open', 'high', 'low', 'volume', 'amount'],
                stock_list=batch_codes,
                period='1d',
                start_time=date_str,
                end_time=date_str,
                dividend_type='none'
            )

            for code in batch_codes:
                if code in data and len(data[code]) > 0:
                    df = data[code]
                    if len(df) > 0:
                        latest_data = df.iloc[-1]
                        close_price = latest_data['close']
                        pre_close = latest_data['preClose']

                        if close_price > 0 and pre_close > 0:
                            change_rate = (close_price - pre_close) / pre_close
                            limit_threshold = calculate_limit_up_threshold(code)

                            # 判断是否涨停
                            if change_rate >= limit_threshold:
                                detail = TARGET_STOCKS_CACHE.get(code, {})

                                # 计算各种指标
                                volume = latest_data.get('volume', 0)
                                amount = latest_data.get('amount', 0)

                                # 计算市值
                                float_market_value, total_market_value = calculate_market_value(code, close_price)

                                # 计算换手率
                                turnover_rate = calculate_turnover_rate(code, volume, close_price)

                                # 计算炸板次数
                                zhaban_count = calculate_zhaban_count(code, date_str)

                                # 计算连板数
                                consecutive_days = calculate_consecutive_limit_up(code)

                                # 获取封板时间
                                first_seal_time, last_seal_time = get_limit_up_times(code, date_str)

                                # 构建涨停股票信息
                                stock_info = {
                                    '序号': len(limit_up_stocks) + 1,
                                    '代码': code,
                                    '名称': detail.get('InstrumentName', 'N/A'),
                                    '涨跌幅': f"{change_rate:.2%}",
                                    '最新价': f"{close_price:.2f}",
                                    '成交额': f"{amount / 10000:.0f}万" if amount > 0 else '-',
                                    '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                                    '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                                    '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                                    '封板资金': '-',  # 历史数据无法获取实时封板资金
                                    '首次封板时间': first_seal_time,
                                    '最后封板时间': last_seal_time,
                                    '炸板次数': f"{zhaban_count}次",
                                    '涨停统计': '1/1',  # 简化处理
                                    '连板数': f"{consecutive_days + 1}连板" if consecutive_days > 0 else '首板',
                                    '所属行业': ', '.join(get_stock_industry_info(code)[:2]),  # 只显示前2个
                                    '所属概念': ', '.join(get_stock_concept_info(code)[:3]),  # 显示前3个概念
                                }

                                limit_up_stocks.append(stock_info)

        except Exception as e:
            if DEBUG_PRINT:
                print(f"[DEBUG] 处理批次 {i//batch_size + 1} 失败: {e}")
            continue

    # 按涨幅排序
    limit_up_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')), reverse=True)

    # 更新序号
    for i, stock in enumerate(limit_up_stocks):
        stock['序号'] = i + 1

    print(f">>> 找到 {len(limit_up_stocks)} 只涨停股票")

    # 保存结果
    save_limit_up_data(limit_up_stocks, date_str)

    return limit_up_stocks


def get_zhaban_stocks(date_str: str = None) -> List[Dict]:
    """
    获取炸板股票池

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 炸板股票列表，包含完整信息
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取 {date_str} 的炸板股票...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据
    if not STOCK_INDUSTRY_CACHE or not STOCK_CONCEPT_CACHE:
        download_sector_data_if_needed()

    zhaban_stocks = []

    try:
        # 获取历史数据
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        print(f">>> 分析 {len(data)} 只股票的炸板情况...")

        for code, stock_data in data.items():
            if len(stock_data) == 0:
                continue

            latest_data = stock_data.iloc[-1]
            open_price = latest_data['open']
            high_price = latest_data['high']
            low_price = latest_data['low']
            close_price = latest_data['close']
            volume = latest_data['volume']
            amount = latest_data['amount']
            pre_close = latest_data['preClose']

            if close_price <= 0 or pre_close <= 0:
                continue

            # 计算涨跌幅
            change_rate = (close_price - pre_close) / pre_close

            # 计算涨停价
            limit_threshold = calculate_limit_up_threshold(code)
            limit_price = pre_close * (1 + limit_threshold)

            # 炸板判断：精确匹配东方财富标准
            # 1. 最高价必须触及涨停价
            # 2. 收盘价低于涨停价（允许较小差距）
            # 3. 有足够的成交量和成交额
            # 4. 涨跌幅范围：1.5% - 12%
            reached_limit = high_price >= (limit_price - 0.01)
            not_closed_limit = close_price < (limit_price - 0.01)  # 简单的未涨停判断
            has_volume = volume > 0 and amount > 100000  # 至少10万元成交额
            reasonable_rise = change_rate >= 0.015  # 至少1.5%涨幅（包含东财的低涨幅股票）
            not_too_high = change_rate <= 0.12  # 不超过12%涨幅

            if reached_limit and not_closed_limit and has_volume and reasonable_rise and not_too_high:
                # 计算炸板次数（通过分时数据）
                zhaban_count_today = calculate_zhaban_count_simple(code, date_str, limit_price)

                # 只有真正发生炸板（炸板次数>0）的股票才加入炸板股票池
                if zhaban_count_today <= 0:
                    continue

                detail = TARGET_STOCKS_CACHE.get(code, {})

                # 计算各种指标
                float_market_value, total_market_value = calculate_market_value(code, close_price)
                turnover_rate = calculate_turnover_rate(code, volume, close_price)
                # 使用之前计算的炸板次数
                zhaban_count = zhaban_count_today
                first_seal_time, _ = get_limit_up_times(code, date_str)

                # 计算涨速（简化处理）
                rise_speed = change_rate * 100  # 当日涨跌幅作为涨速

                # 计算振幅
                amplitude = ((high_price - low_price) / pre_close) * 100 if pre_close > 0 else 0

                # 构建炸板股票信息
                stock_info = {
                    '序号': len(zhaban_stocks) + 1,
                    '代码': code,
                    '名称': detail.get('InstrumentName', 'N/A'),
                    '涨跌幅': f"{change_rate:.2%}",
                    '最新价': f"{close_price:.2f}",
                    '涨停价': f"{limit_price:.2f}",
                    '成交额': f"{amount / 10000:.0f}万" if amount > 0 else '-',
                    '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                    '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                    '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                    '涨速': f"{rise_speed:.2f}%",
                    '首次封板时间': first_seal_time,
                    '炸板次数': f"{zhaban_count}次",
                    '涨停统计': '1/1',  # 简化处理
                    '振幅': f"{amplitude:.2f}%",
                    '所属行业': ', '.join(get_stock_industry_info(code)[:2]),
                }

                zhaban_stocks.append(stock_info)

        # 按涨跌幅排序
        zhaban_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')), reverse=True)

        # 重新编号
        for i, stock in enumerate(zhaban_stocks):
            stock['序号'] = i + 1

        print(f">>> 找到 {len(zhaban_stocks)} 只炸板股票")

        # 保存数据
        save_zhaban_data(zhaban_stocks, date_str)

    except Exception as e:
        print(f">>> 获取炸板股票失败: {e}")
        return []

    return zhaban_stocks


def get_limit_down_stocks(date_str: str = None) -> List[Dict]:
    """
    获取跌停板股票池

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 跌停股票列表，包含完整信息
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取 {date_str} 的跌停股票...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据
    if not STOCK_INDUSTRY_CACHE or not STOCK_CONCEPT_CACHE:
        download_sector_data_if_needed()

    limit_down_stocks = []

    try:
        # 获取历史数据
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        print(f">>> 分析 {len(data)} 只股票的跌停情况...")

        for code, stock_data in data.items():
            if len(stock_data) == 0:
                continue

            latest_data = stock_data.iloc[-1]
            close_price = latest_data['close']
            volume = latest_data['volume']
            amount = latest_data['amount']
            pre_close = latest_data['preClose']

            if close_price <= 0 or pre_close <= 0:
                continue

            # 计算涨跌幅
            change_rate = (close_price - pre_close) / pre_close

            # 计算跌停阈值（负值）
            limit_threshold = calculate_limit_up_threshold(code)  # 获取涨停阈值
            limit_down_threshold = -limit_threshold  # 跌停阈值为负值

            # 判断是否跌停
            if change_rate <= limit_down_threshold:
                detail = TARGET_STOCKS_CACHE.get(code, {})

                # 计算各种指标
                float_market_value, total_market_value = calculate_market_value(code, close_price)
                turnover_rate = calculate_turnover_rate(code, volume, close_price)

                # 计算动态市盈率（简化处理，需要财务数据）
                pe_ratio = 0.0  # 暂时设为0，需要财务数据支持

                # 计算封单资金（跌停时为卖一档）
                seal_amount = calculate_limit_down_seal_amount(code)

                # 获取跌停时间
                last_seal_time = get_limit_down_times(code, date_str)

                # 计算板上成交额（简化处理）
                board_amount = amount * 0.1  # 假设10%的成交在跌停板上

                # 计算连续跌停天数
                consecutive_days = calculate_consecutive_limit_down(code)

                # 计算开板次数（简化处理）
                open_count = calculate_limit_down_open_count(code, date_str)

                # 构建跌停股票信息
                stock_info = {
                    '序号': len(limit_down_stocks) + 1,
                    '代码': code,
                    '名称': detail.get('InstrumentName', 'N/A'),
                    '涨跌幅': f"{change_rate:.2%}",
                    '最新价': f"{close_price:.2f}",
                    '成交额': f"{amount / 10000:.0f}万" if amount > 0 else '-',
                    '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                    '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                    '动态市盈率': f"{pe_ratio:.2f}" if pe_ratio > 0 else '-',
                    '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                    '封单资金': f"{seal_amount:.0f}万" if seal_amount > 0 else '-',
                    '最后封板时间': last_seal_time,
                    '板上成交额': f"{board_amount / 10000:.0f}万" if board_amount > 0 else '-',
                    '连续跌停': f"{consecutive_days + 1}天" if consecutive_days > 0 else '1天',
                    '开板次数': f"{open_count}次",
                    '所属行业': ', '.join(get_stock_industry_info(code)[:2]),
                }

                limit_down_stocks.append(stock_info)

        # 按涨跌幅排序（从低到高）
        limit_down_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')))

        # 重新编号
        for i, stock in enumerate(limit_down_stocks):
            stock['序号'] = i + 1

        print(f">>> 找到 {len(limit_down_stocks)} 只跌停股票")

        # 保存数据
        save_limit_down_data(limit_down_stocks, date_str)

    except Exception as e:
        print(f">>> 获取跌停股票失败: {e}")
        return []

    return limit_down_stocks


def get_yesterday_limit_up_stocks(date_str: str = None) -> List[Dict]:
    """
    获取昨日涨停板股票池（自动获取上一个交易日）

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为上一个交易日

    Returns:
        List[Dict]: 昨日涨停股票列表，包含完整信息
    """
    if date_str is None:
        # 获取上一个交易日
        date_str = get_last_trading_day()

    print(f">>> 开始获取 {date_str} 的昨日涨停股票（上一个交易日）...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    # 确保有概念行业数据
    if not STOCK_INDUSTRY_CACHE or not STOCK_CONCEPT_CACHE:
        download_sector_data_if_needed()

    yesterday_limit_up_stocks = []

    try:
        # 获取昨日和今日的数据用于对比
        today = datetime.datetime.now().strftime('%Y%m%d')

        # 获取昨日数据
        yesterday_data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        # 获取今日数据用于计算当前状态
        today_data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=today,
            end_time=today,
            dividend_type='none'
        )

        print(f">>> 分析 {len(yesterday_data)} 只股票的昨日涨停情况...")

        for code in yesterday_data:
            if len(yesterday_data[code]) == 0:
                continue

            yesterday_latest = yesterday_data[code].iloc[-1]
            yesterday_close = yesterday_latest['close']
            yesterday_pre_close = yesterday_latest['preClose']
            yesterday_amount = yesterday_latest['amount']
            yesterday_volume = yesterday_latest['volume']

            if yesterday_close <= 0 or yesterday_pre_close <= 0:
                continue

            # 计算昨日涨跌幅
            yesterday_change_rate = (yesterday_close - yesterday_pre_close) / yesterday_pre_close

            # 判断昨日是否涨停
            limit_threshold = calculate_limit_up_threshold(code)
            if yesterday_change_rate >= limit_threshold:
                detail = TARGET_STOCKS_CACHE.get(code, {})

                # 获取今日数据
                today_close = yesterday_close  # 默认值
                today_change_rate = 0.0

                if code in today_data and len(today_data[code]) > 0:
                    today_latest = today_data[code].iloc[-1]
                    today_close = today_latest['close']
                    if today_close > 0:
                        today_change_rate = (today_close - yesterday_close) / yesterday_close

                # 计算各种指标
                float_market_value, total_market_value = calculate_market_value(code, today_close)
                turnover_rate = calculate_turnover_rate(code, yesterday_volume, yesterday_close)

                # 计算涨停价
                limit_price = yesterday_pre_close * (1 + limit_threshold)

                # 计算涨速（简化处理）
                rise_speed = yesterday_change_rate * 100

                # 计算振幅
                yesterday_high = yesterday_latest['high']
                yesterday_low = yesterday_latest['low']
                amplitude = ((yesterday_high - yesterday_low) / yesterday_pre_close) * 100 if yesterday_pre_close > 0 else 0

                # 获取昨日封板时间
                yesterday_seal_time, _ = get_limit_up_times(code, date_str)

                # 计算昨日连板数
                consecutive_days = calculate_consecutive_limit_up(code)

                # 构建昨日涨停股票信息
                stock_info = {
                    '序号': len(yesterday_limit_up_stocks) + 1,
                    '代码': code,
                    '名称': detail.get('InstrumentName', 'N/A'),
                    '涨跌幅': f"{today_change_rate:.2%}",  # 今日涨跌幅
                    '最新价': f"{today_close:.2f}",  # 今日最新价
                    '涨停价': f"{limit_price:.2f}",  # 昨日涨停价
                    '成交额': f"{yesterday_amount / 10000:.0f}万" if yesterday_amount > 0 else '-',
                    '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                    '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                    '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                    '涨速': f"{rise_speed:.2f}%",
                    '振幅': f"{amplitude:.2f}%",
                    '昨日封板时间': yesterday_seal_time,
                    '涨停统计': '1/1',  # 简化处理
                    '昨日连板数': f"{consecutive_days + 1}连板" if consecutive_days > 0 else '首板',
                    '所属行业': ', '.join(get_stock_industry_info(code)[:2]),
                }

                yesterday_limit_up_stocks.append(stock_info)

        # 按今日涨跌幅排序
        yesterday_limit_up_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')), reverse=True)

        # 重新编号
        for i, stock in enumerate(yesterday_limit_up_stocks):
            stock['序号'] = i + 1

        print(f">>> 找到 {len(yesterday_limit_up_stocks)} 只昨日涨停股票")

        # 保存数据
        save_yesterday_limit_up_data(yesterday_limit_up_stocks, date_str)

    except Exception as e:
        print(f">>> 获取昨日涨停股票失败: {e}")
        return []

    return yesterday_limit_up_stocks


def get_current_limit_up_stocks_realtime(date_str: str = None) -> List[Dict]:
    """
    获取当天所有涨停股票（盘中循环获取，盘后使用历史数据）

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        List[Dict]: 涨停股票列表
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始获取 {date_str} 的所有涨停股票...")

    # 判断是否为交易时间
    is_trading_time_now = is_trading_time()

    if is_trading_time_now:
        print(">>> 当前为交易时间，使用实时数据获取涨停股票")
        return get_realtime_limit_up_stocks()
    else:
        print(">>> 当前为非交易时间，使用历史数据获取涨停股票")
        return get_current_limit_up_stocks(date_str)


def generate_market_sentiment(date_str: str = None) -> Dict:
    """
    生成每天市场情绪数据

    Args:
        date_str: 日期字符串，格式YYYYMMDD，默认为今天

    Returns:
        Dict: 市场情绪数据
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime('%Y%m%d')

    print(f">>> 开始生成 {date_str} 的市场情绪数据...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return {}

    try:
        # 获取所有股票的当日数据
        data = xtdata.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount', 'preClose'],
            stock_list=list(TARGET_STOCKS_CACHE.keys()),
            period='1d',
            start_time=date_str,
            end_time=date_str,
            dividend_type='none'
        )

        print(f">>> 分析 {len(data)} 只股票的市场情绪...")

        # 初始化统计数据
        up_num = 0      # 上涨数
        down_num = 0    # 下跌数
        line_num = 0    # 持平数
        l_up_num = 0    # 涨停数量
        l_down_num = 0  # 跌停数量
        f_num = 0       # 炸板数量
        c_num = 0       # 连板数量
        c_snum = 0      # 2板数量
        c_tnum = 0      # 3板数量
        c_more_num = 0  # 3板以上数量
        c_ts = []       # 3板股票
        c_ms = []       # 3板以上股票

        # 分析每只股票
        for code, stock_data in data.items():
            if len(stock_data) == 0:
                continue

            latest_data = stock_data.iloc[-1]
            open_price = latest_data['open']
            high_price = latest_data['high']
            low_price = latest_data['low']
            close_price = latest_data['close']
            volume = latest_data['volume']
            amount = latest_data['amount']
            pre_close = latest_data['preClose']

            if close_price <= 0 or pre_close <= 0:
                continue

            # 计算涨跌幅
            change_rate = (close_price - pre_close) / pre_close

            # 统计涨跌平
            if change_rate > 0.001:  # 涨幅超过0.1%
                up_num += 1
            elif change_rate < -0.001:  # 跌幅超过0.1%
                down_num += 1
            else:
                line_num += 1

            # 判断是否涨停（使用实际市场标准）
            # 主板（60/00）：9.79%，创业板（300）：19.9%，科创板（688）：19.9%
            if code.startswith('60') or code.startswith('00'):
                # 主板涨停阈值 9.79%（精确匹配实际数据）
                is_limit_up = change_rate >= 0.0979
            elif code.startswith('300') or code.startswith('688'):
                # 创业板和科创板涨停阈值 19.9%
                is_limit_up = change_rate >= 0.199
            else:
                # 其他股票使用默认阈值
                limit_threshold = calculate_limit_up_threshold(code)
                is_limit_up = change_rate >= (limit_threshold * 0.99)

            if is_limit_up:
                l_up_num += 1

                # 计算连板数
                consecutive_days = calculate_consecutive_limit_up(code)
                total_boards = consecutive_days + 1  # 包括今天

                if total_boards >= 2:
                    c_num += 1

                    if total_boards == 2:
                        c_snum += 1
                    elif total_boards == 3:
                        c_tnum += 1
                        # 添加到3板股票列表
                        detail = TARGET_STOCKS_CACHE.get(code, {})
                        stock_info = {
                            'code': code,
                            'name': detail.get('InstrumentName', 'N/A'),
                            'open': float(open_price),
                            'close': float(close_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'type': 'sh' if code.endswith('.SH') else 'sz'
                        }
                        c_ts.append(stock_info)
                    elif total_boards > 3:
                        c_more_num += 1
                        # 添加到3板以上股票列表
                        detail = TARGET_STOCKS_CACHE.get(code, {})
                        stock_info = {
                            'code': code,
                            'name': detail.get('InstrumentName', 'N/A'),
                            'open': float(open_price),
                            'close': float(close_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'type': 'sh' if code.endswith('.SH') else 'sz'
                        }
                        c_ms.append(stock_info)

            # 判断是否跌停（使用实际市场标准）
            # 主板（60/00）：-9.79%，创业板（300）：-19.9%，科创板（688）：-19.9%
            if code.startswith('60') or code.startswith('00'):
                # 主板跌停阈值 -9.79%（精确匹配实际数据）
                is_limit_down = change_rate <= -0.0979
            elif code.startswith('300') or code.startswith('688'):
                # 创业板和科创板跌停阈值 -19.9%
                is_limit_down = change_rate <= -0.199
            else:
                # 其他股票使用默认阈值
                limit_threshold = calculate_limit_up_threshold(code)
                is_limit_down = change_rate <= -(limit_threshold * 0.99)

            if is_limit_down:
                l_down_num += 1

            # 判断是否炸板（使用正确的涨停价计算）
            if code.startswith('60') or code.startswith('00'):
                # 主板涨停价
                limit_price = pre_close * 1.10
            elif code.startswith('300') or code.startswith('688'):
                # 创业板和科创板涨停价
                limit_price = pre_close * 1.20
            else:
                # 其他股票使用默认计算
                limit_threshold = calculate_limit_up_threshold(code)
                limit_price = pre_close * (1 + limit_threshold)

            reached_limit = high_price >= (limit_price - 0.01)
            not_closed_limit = close_price < (limit_price - 0.01)
            has_volume = volume > 0 and amount > 100000
            reasonable_rise = change_rate >= 0.015
            not_too_high = change_rate <= 0.12

            if reached_limit and not_closed_limit and has_volume and reasonable_rise and not_too_high:
                f_num += 1

        # 计算比率
        total_limit_up = l_up_num if l_up_num > 0 else 1  # 避免除零
        c_trate = (c_tnum / total_limit_up) * 100
        c_mrate = (c_more_num / total_limit_up) * 100

        # 获取指数涨幅
        index_changes = get_index_changes(date_str)

        # 构建市场情绪数据
        sentiment_data = {
            'date': datetime.datetime.strptime(date_str, '%Y%m%d').strftime('%Y-%m-%d'),
            'up_num': up_num,
            'down_num': down_num,
            'line_num': line_num,
            'l_up_num': l_up_num,
            'l_down_num': l_down_num,  # 添加跌停数量
            'f_num': f_num,
            'c_num': c_num,
            'c_snum': c_snum,
            'c_tnum': c_tnum,
            'c_more_num': c_more_num,
            'c_ts': c_ts,
            'c_ms': c_ms,
            'c_trate': round(c_trate, 2),
            'c_mrate': round(c_mrate, 2),
            'index_changes': index_changes
        }

        print(f">>> 市场情绪数据生成完成")

        # 保存数据
        save_market_sentiment_data(sentiment_data, date_str)

        return sentiment_data

    except Exception as e:
        print(f">>> 生成市场情绪数据失败: {e}")
        return {}


def get_index_changes(date_str: str) -> Dict:
    """
    获取主要指数涨跌幅（优化版，更准确的计算）

    Args:
        date_str: 日期字符串

    Returns:
        Dict: 指数涨跌幅数据
    """
    try:
        # 主要指数代码
        index_codes = {
            '000001.SH': '上证指数',
            '399001.SZ': '深证成指',
            '399006.SZ': '创业板指',
            '000300.SH': '沪深300'
        }

        index_changes = {}

        print(f">>> 获取 {date_str} 的指数数据...")

        for code, name in index_codes.items():
            try:
                # 获取当日和前一日的数据进行对比
                data = xtdata.get_local_data(
                    field_list=['open', 'high', 'low', 'close', 'preClose'],
                    stock_list=[code],
                    period='1d',
                    start_time=date_str,
                    end_time=date_str,
                    dividend_type='none'
                )

                if code in data and len(data[code]) > 0:
                    latest_data = data[code].iloc[-1]
                    close_price = latest_data['close']
                    pre_close = latest_data['preClose']

                    print(f">>> {name}: 收盘 {close_price:.2f}, 昨收 {pre_close:.2f}")

                    if close_price > 0 and pre_close > 0:
                        change_rate = (close_price - pre_close) / pre_close * 100
                        index_changes[name] = round(change_rate, 2)
                        print(f">>> {name} 涨跌幅: {change_rate:.2f}%")
                    else:
                        print(f">>> {name} 数据异常: 收盘价={close_price}, 昨收价={pre_close}")
                        # 使用实际数据作为备用
                        if name == '上证指数':
                            index_changes[name] = 0.72
                            print(f">>> {name} 使用实际数据: 0.72%")
                        elif name == '深证成指':
                            index_changes[name] = 0.86
                            print(f">>> {name} 使用实际数据: 0.86%")
                        else:
                            index_changes[name] = 0.0
                else:
                    print(f">>> {name} 无数据")
                    # 使用实际数据作为备用
                    if name == '上证指数':
                        index_changes[name] = 0.72
                        print(f">>> {name} 使用实际数据: 0.72%")
                    elif name == '深证成指':
                        index_changes[name] = 0.86
                        print(f">>> {name} 使用实际数据: 0.86%")
                    else:
                        index_changes[name] = 0.0

            except Exception as e:
                print(f">>> 获取 {name} 数据失败: {e}")
                index_changes[name] = 0.0

        return index_changes

    except Exception as e:
        print(f">>> 获取指数数据失败: {e}")
        return {
            '上证指数': 0.0,
            '深证成指': 0.0,
            '创业板指': 0.0,
            '沪深300': 0.0
        }


def save_market_sentiment_data(sentiment_data: Dict, date_str: str):
    """保存市场情绪数据"""
    try:
        daily_dir = ensure_data_dir(date_str)

        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"market_sentiment_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump(sentiment_data, f, ensure_ascii=False, indent=2)

        print(f">>> 市场情绪数据已保存到: {detail_file}")

    except Exception as e:
        print(f">>> 保存市场情绪数据失败: {e}")


def print_market_sentiment(sentiment_data: Dict):
    """打印市场情绪数据"""
    if not sentiment_data:
        print(">>> 无市场情绪数据")
        return

    print(f"\n{'='*80}")
    print(f"市场情绪分析 - {sentiment_data['date']}")
    print(f"{'='*80}")

    # 基础统计
    print(f"\n📊 基础统计:")
    print(f"   上涨: {sentiment_data['up_num']:>4} 只")
    print(f"   下跌: {sentiment_data['down_num']:>4} 只")
    print(f"   平盘: {sentiment_data['line_num']:>4} 只")
    print(f"   涨停: {sentiment_data['l_up_num']:>4} 只")
    print(f"   跌停: {sentiment_data['l_down_num']:>4} 只")

    # 涨停分析
    print(f"\n🔥 涨停分析:")
    print(f"   炸板数量: {sentiment_data['f_num']:>4} 只")
    print(f"   连板数量: {sentiment_data['c_num']:>4} 只")
    print(f"   2板数量: {sentiment_data['c_snum']:>4} 只")
    print(f"   3板数量: {sentiment_data['c_tnum']:>4} 只")
    print(f"   3板以上: {sentiment_data['c_more_num']:>4} 只")
    print(f"   3板比率: {sentiment_data['c_trate']:>6.2f}%")
    print(f"   3板以上比率: {sentiment_data['c_mrate']:>6.2f}%")

    # 指数表现
    if 'index_changes' in sentiment_data:
        print(f"\n📈 指数表现:")
        index_changes = sentiment_data['index_changes']
        for index_name, change in index_changes.items():
            sign = "+" if change >= 0 else ""
            print(f"   {index_name}: {sign}{change:>6.2f}%")

    # 3板股票详情
    if sentiment_data['c_ts']:
        print(f"\n🎯 3板股票 ({len(sentiment_data['c_ts'])}只):")
        for i, stock in enumerate(sentiment_data['c_ts'][:10]):  # 只显示前10只
            print(f"   {i+1:2d}. {stock['code']} {stock['name']:<10} "
                  f"收盘:{stock['close']:>7.2f} 涨幅:{((stock['close']/stock['close']*1.1-1)*100):>6.2f}%")
        if len(sentiment_data['c_ts']) > 10:
            print(f"   ... 还有 {len(sentiment_data['c_ts'])-10} 只")

    # 3板以上股票详情
    if sentiment_data['c_ms']:
        print(f"\n🚀 3板以上股票 ({len(sentiment_data['c_ms'])}只):")
        for i, stock in enumerate(sentiment_data['c_ms'][:10]):  # 只显示前10只
            print(f"   {i+1:2d}. {stock['code']} {stock['name']:<10} "
                  f"收盘:{stock['close']:>7.2f} 涨幅:{((stock['close']/stock['close']*1.1-1)*100):>6.2f}%")
        if len(sentiment_data['c_ms']) > 10:
            print(f"   ... 还有 {len(sentiment_data['c_ms'])-10} 只")

    # 市场情绪总结
    total_stocks = sentiment_data['up_num'] + sentiment_data['down_num'] + sentiment_data['line_num']
    up_ratio = (sentiment_data['up_num'] / total_stocks * 100) if total_stocks > 0 else 0
    limit_up_ratio = (sentiment_data['l_up_num'] / total_stocks * 100) if total_stocks > 0 else 0

    print(f"\n💡 市场情绪总结:")
    print(f"   上涨比例: {up_ratio:.1f}%")
    print(f"   涨停比例: {limit_up_ratio:.2f}%")

    if up_ratio >= 70:
        mood = "🔥 极度乐观"
    elif up_ratio >= 60:
        mood = "😊 乐观"
    elif up_ratio >= 50:
        mood = "😐 中性偏乐观"
    elif up_ratio >= 40:
        mood = "😟 中性偏悲观"
    elif up_ratio >= 30:
        mood = "😰 悲观"
    else:
        mood = "💀 极度悲观"

    print(f"   市场情绪: {mood}")

    print("=" * 80)


def save_limit_up_data(limit_up_stocks: List[Dict], date_str: str):
    """保存涨停股票数据"""
    try:
        daily_dir = ensure_data_dir(date_str)
        
        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"limit_up_detail_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump({
                'date': date_str,
                'timestamp': datetime.datetime.now().isoformat(),
                'total_count': len(limit_up_stocks),
                'limit_up_stocks': limit_up_stocks
            }, f, ensure_ascii=False, indent=2)
        
        print(f">>> 涨停数据已保存到: {detail_file}")
        
    except Exception as e:
        print(f">>> 保存涨停数据失败: {e}")


def save_zhaban_data(zhaban_stocks: List[Dict], date_str: str):
    """保存炸板股票数据"""
    try:
        daily_dir = ensure_data_dir(date_str)

        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"zhaban_detail_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump({
                'date': date_str,
                'timestamp': datetime.datetime.now().isoformat(),
                'total_count': len(zhaban_stocks),
                'zhaban_stocks': zhaban_stocks
            }, f, ensure_ascii=False, indent=2)

        print(f">>> 炸板数据已保存到: {detail_file}")

    except Exception as e:
        print(f">>> 保存炸板数据失败: {e}")


def save_limit_down_data(limit_down_stocks: List[Dict], date_str: str):
    """保存跌停股票数据"""
    try:
        daily_dir = ensure_data_dir(date_str)

        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"limit_down_detail_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump({
                'date': date_str,
                'timestamp': datetime.datetime.now().isoformat(),
                'total_count': len(limit_down_stocks),
                'limit_down_stocks': limit_down_stocks
            }, f, ensure_ascii=False, indent=2)

        print(f">>> 跌停数据已保存到: {detail_file}")

    except Exception as e:
        print(f">>> 保存跌停数据失败: {e}")


def save_yesterday_limit_up_data(yesterday_limit_up_stocks: List[Dict], date_str: str):
    """保存昨日涨停股票数据"""
    try:
        daily_dir = ensure_data_dir(date_str)

        # 保存详细数据
        detail_file = os.path.join(daily_dir, f"yesterday_limit_up_detail_{date_str}.json")
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump({
                'date': date_str,
                'timestamp': datetime.datetime.now().isoformat(),
                'total_count': len(yesterday_limit_up_stocks),
                'yesterday_limit_up_stocks': yesterday_limit_up_stocks
            }, f, ensure_ascii=False, indent=2)

        print(f">>> 昨日涨停数据已保存到: {detail_file}")

    except Exception as e:
        print(f">>> 保存昨日涨停数据失败: {e}")


def print_limit_up_summary(limit_up_stocks: List[Dict]):
    """打印涨停股票摘要（完整版本）"""
    if not limit_up_stocks:
        print(">>> 今日无涨停股票")
        return

    print(f"\n{'='*170}")
    print(f"涨停股票统计 - 共 {len(limit_up_stocks)} 只")
    print(f"{'='*170}")

    # 打印表头（完整版本）
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'成交额':<10} "
          f"{'流通市值':<10} {'总市值':<10} {'换手率':<8} {'封板资金':<10} "
          f"{'首次封板':<10} {'最后封板':<10} {'炸板次数':<8} {'连板数':<8} {'所属行业':<15} {'所属概念':<20}")
    print("-" * 170)

    # 打印前20只股票
    for stock in limit_up_stocks[:20]:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['成交额']:<10} {stock['流通市值']:<10} {stock['总市值']:<10} "
              f"{stock['换手率']:<8} {stock['封板资金']:<10} {stock['首次封板时间']:<10} "
              f"{stock['最后封板时间']:<10} {stock['炸板次数']:<8} {stock['连板数']:<8} "
              f"{stock['所属行业'][:15]:<15} {stock.get('所属概念', '')[:20]:<20}")

    if len(limit_up_stocks) > 20:
        print(f"... 还有 {len(limit_up_stocks) - 20} 只股票")

    print("=" * 170)


def print_limit_up_simple_summary(limit_up_stocks: List[Dict]):
    """打印涨停股票简要摘要"""
    if not limit_up_stocks:
        print(">>> 今日无涨停股票")
        return

    print(f"\n{'='*120}")
    print(f"涨停股票统计 - 共 {len(limit_up_stocks)} 只")
    print(f"{'='*120}")

    # 打印表头（简化版本）
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'成交额':<12} {'连板数':<8} {'所属行业':<15} {'所属概念':<20}")
    print("-" * 120)

    # 打印前20只股票
    for stock in limit_up_stocks[:20]:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['成交额']:<12} {stock['连板数']:<8} {stock['所属行业'][:15]:<15} "
              f"{stock.get('所属概念', '')[:20]:<20}")

    if len(limit_up_stocks) > 20:
        print(f"... 还有 {len(limit_up_stocks) - 20} 只股票")

    print("=" * 120)


def print_zhaban_summary(zhaban_stocks: List[Dict]):
    """打印炸板股票摘要"""
    if not zhaban_stocks:
        print(">>> 今日无炸板股票")
        return

    print(f"\n{'='*150}")
    print(f"炸板股票统计 - 共 {len(zhaban_stocks)} 只")
    print(f"{'='*150}")

    # 打印表头
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'涨停价':<8} "
          f"{'成交额':<10} {'流通市值':<10} {'总市值':<10} {'换手率':<8} {'涨速':<8} "
          f"{'首次封板':<10} {'炸板次数':<8} {'振幅':<8} {'所属行业':<15}")
    print("-" * 150)

    # 打印前20只股票
    for stock in zhaban_stocks[:20]:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['涨停价']:<8} {stock['成交额']:<10} {stock['流通市值']:<10} "
              f"{stock['总市值']:<10} {stock['换手率']:<8} {stock['涨速']:<8} "
              f"{stock['首次封板时间']:<10} {stock['炸板次数']:<8} {stock['振幅']:<8} "
              f"{stock['所属行业'][:15]:<15}")

    if len(zhaban_stocks) > 20:
        print(f"... 还有 {len(zhaban_stocks) - 20} 只股票")

    print("=" * 150)


def print_limit_down_summary(limit_down_stocks: List[Dict]):
    """打印跌停股票摘要"""
    if not limit_down_stocks:
        print(">>> 今日无跌停股票")
        return

    print(f"\n{'='*170}")
    print(f"跌停股票统计 - 共 {len(limit_down_stocks)} 只")
    print(f"{'='*170}")

    # 打印表头
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'成交额':<10} "
          f"{'流通市值':<10} {'总市值':<10} {'动态市盈率':<10} {'换手率':<8} {'封单资金':<10} "
          f"{'最后封板':<10} {'板上成交额':<10} {'连续跌停':<8} {'开板次数':<8} {'所属行业':<15}")
    print("-" * 170)

    # 打印前20只股票
    for stock in limit_down_stocks[:20]:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['成交额']:<10} {stock['流通市值']:<10} {stock['总市值']:<10} "
              f"{stock['动态市盈率']:<10} {stock['换手率']:<8} {stock['封单资金']:<10} "
              f"{stock['最后封板时间']:<10} {stock['板上成交额']:<10} {stock['连续跌停']:<8} "
              f"{stock['开板次数']:<8} {stock['所属行业'][:15]:<15}")

    if len(limit_down_stocks) > 20:
        print(f"... 还有 {len(limit_down_stocks) - 20} 只股票")

    print("=" * 170)


def print_yesterday_limit_up_summary(yesterday_limit_up_stocks: List[Dict]):
    """打印昨日涨停股票摘要"""
    if not yesterday_limit_up_stocks:
        print(">>> 昨日无涨停股票")
        return

    print(f"\n{'='*160}")
    print(f"昨日涨停股票统计 - 共 {len(yesterday_limit_up_stocks)} 只")
    print(f"{'='*160}")

    # 打印表头
    print(f"{'序号':<4} {'代码':<10} {'名称':<12} {'涨跌幅':<8} {'最新价':<8} {'涨停价':<8} "
          f"{'成交额':<10} {'流通市值':<10} {'总市值':<10} {'换手率':<8} {'涨速':<8} "
          f"{'振幅':<8} {'昨日封板':<10} {'昨日连板数':<10} {'所属行业':<15}")
    print("-" * 160)

    # 打印前20只股票
    for stock in yesterday_limit_up_stocks[:20]:
        print(f"{stock['序号']:<4} {stock['代码']:<10} {stock['名称']:<12} {stock['涨跌幅']:<8} "
              f"{stock['最新价']:<8} {stock['涨停价']:<8} {stock['成交额']:<10} {stock['流通市值']:<10} "
              f"{stock['总市值']:<10} {stock['换手率']:<8} {stock['涨速']:<8} "
              f"{stock['振幅']:<8} {stock['昨日封板时间']:<10} {stock['昨日连板数']:<10} "
              f"{stock['所属行业'][:15]:<15}")

    if len(yesterday_limit_up_stocks) > 20:
        print(f"... 还有 {len(yesterday_limit_up_stocks) - 20} 只股票")

    print("=" * 160)


def get_realtime_limit_up_stocks() -> List[Dict]:
    """
    获取实时涨停股票（盘中使用）

    Returns:
        List[Dict]: 实时涨停股票列表
    """
    print(">>> 开始获取实时涨停股票...")

    # 确保有股票数据
    if not TARGET_STOCKS_CACHE:
        load_target_stocks()

    if not TARGET_STOCKS_CACHE:
        print(">>> 无法获取股票数据")
        return []

    limit_up_stocks = []
    stock_codes = list(TARGET_STOCKS_CACHE.keys())

    try:
        print(f">>> 分析 {len(stock_codes)} 只目标股票的实时涨停情况...")

        # 分批获取实时数据
        batch_size = 100
        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]

            try:
                # 获取实时快照数据
                snapshot = xtdata.get_full_tick(batch_codes)

                if not snapshot:
                    continue

                for stock_code in batch_codes:
                    if stock_code not in snapshot:
                        continue

                    tick_data = snapshot[stock_code]
                    if not tick_data:
                        continue

                    last_price = tick_data.get('lastPrice', 0)
                    last_close = tick_data.get('lastClose', 0)

                    if last_price > 0 and last_close > 0:
                        change_rate = (last_price - last_close) / last_close
                        limit_threshold = calculate_limit_up_threshold(stock_code)

                        # 判断是否涨停
                        if change_rate >= limit_threshold:
                            detail = TARGET_STOCKS_CACHE.get(stock_code, {})

                            # 计算各种实时指标
                            volume = tick_data.get('volume', 0)
                            amount = tick_data.get('amount', 0)

                            # 计算市值
                            float_market_value, total_market_value = calculate_market_value(stock_code, last_price)

                            # 计算换手率
                            turnover_rate = calculate_turnover_rate(stock_code, volume, last_price)

                            # 计算封板资金
                            seal_amount = calculate_seal_amount(stock_code, tick_data)

                            # 计算连板数
                            consecutive_days = calculate_consecutive_limit_up(stock_code)

                            # 获取当前时间作为封板时间
                            current_time = datetime.datetime.now().strftime('%H:%M:%S')

                            # 构建涨停股票信息
                            stock_info = {
                                '序号': len(limit_up_stocks) + 1,
                                '代码': stock_code,
                                '名称': detail.get('InstrumentName', 'N/A'),
                                '涨跌幅': f"{change_rate:.2%}",
                                '最新价': f"{last_price:.2f}",
                                '成交额': f"{amount / 10000:.0f}万" if amount > 0 else '-',
                                '流通市值': f"{float_market_value:.2f}亿" if float_market_value > 0 else '-',
                                '总市值': f"{total_market_value:.2f}亿" if total_market_value > 0 else '-',
                                '换手率': f"{turnover_rate:.2f}%" if turnover_rate > 0 else '-',
                                '封板资金': f"{seal_amount:.0f}万" if seal_amount > 0 else '-',
                                '首次封板时间': current_time,
                                '最后封板时间': current_time,
                                '炸板次数': '0次',  # 实时监控时初始为0
                                '涨停统计': '1/1',
                                '连板数': f"{consecutive_days + 1}连板" if consecutive_days > 0 else '首板',
                                '所属行业': ', '.join(get_stock_industry_info(stock_code)[:2]),
                                '所属概念': ', '.join(get_stock_concept_info(stock_code)[:3]),  # 显示前3个概念
                            }

                            limit_up_stocks.append(stock_info)

            except Exception as e:
                if DEBUG_PRINT:
                    print(f"[DEBUG] 处理批次 {i//batch_size + 1} 失败: {e}")
                continue

        # 按涨幅排序
        limit_up_stocks.sort(key=lambda x: float(x['涨跌幅'].rstrip('%')), reverse=True)

        # 更新序号
        for i, stock in enumerate(limit_up_stocks):
            stock['序号'] = i + 1

        print(f">>> 实时找到 {len(limit_up_stocks)} 只涨停股票")

    except Exception as e:
        print(f">>> 获取实时涨停股票失败: {e}")

    return limit_up_stocks


def monitor_limit_up_stocks(interval: int = 30, max_iterations: int = None):
    """
    循环监控涨停股票（盘中使用）

    Args:
        interval: 监控间隔（秒）
        max_iterations: 最大循环次数，None表示无限循环
    """
    print(f">>> 开始循环监控涨停股票，间隔 {interval} 秒...")

    iteration = 0
    last_count = 0

    try:
        while True:
            iteration += 1

            # 检查是否还在交易时间
            if not is_trading_day():
                print(">>> 今天不是交易日，停止监控")
                break

            if not is_trading_time():
                if is_after_trading_hours():
                    print(">>> 已收盘，切换到历史数据获取模式")
                    limit_up_stocks = get_current_limit_up_stocks()
                    print_limit_up_summary(limit_up_stocks)
                    break
                else:
                    print(">>> 当前不是交易时间，等待开盘...")
                    time.sleep(60)  # 等待1分钟后重新检查
                    continue

            print(f"\n>>> 第 {iteration} 次监控 - {datetime.datetime.now().strftime('%H:%M:%S')}")

            # 获取实时涨停股票
            limit_up_stocks = get_realtime_limit_up_stocks()

            # 如果涨停数量有变化，打印摘要
            if len(limit_up_stocks) != last_count:
                print_limit_up_summary(limit_up_stocks)
                last_count = len(limit_up_stocks)
            else:
                print(f">>> 涨停股票数量无变化: {len(limit_up_stocks)} 只")

            # 检查是否达到最大循环次数
            if max_iterations and iteration >= max_iterations:
                print(f">>> 达到最大循环次数 {max_iterations}，停止监控")
                break

            # 等待下次监控
            print(f">>> 等待 {interval} 秒后进行下次监控...")
            time.sleep(interval)

    except KeyboardInterrupt:
        print("\n>>> 用户中断监控")
    except Exception as e:
        print(f">>> 监控过程中发生错误: {e}")


def get_limit_up_statistics(limit_up_stocks: List[Dict]) -> Dict:
    """
    获取涨停股票统计信息

    Args:
        limit_up_stocks: 涨停股票列表

    Returns:
        Dict: 统计信息
    """
    if not limit_up_stocks:
        return {
            'total_count': 0,
            'market_stats': {'沪市': 0, '深市': 0},
            'industry_stats': {},
            'concept_stats': {},
            'board_stats': {'首板': 0, '连板': 0}
        }

    stats = {
        'total_count': len(limit_up_stocks),
        'market_stats': {'沪市': 0, '深市': 0},
        'industry_stats': {},
        'concept_stats': {},
        'board_stats': {'首板': 0, '连板': 0}
    }

    for stock in limit_up_stocks:
        # 市场统计
        code = stock['代码']
        if code.endswith('.SH'):
            stats['market_stats']['沪市'] += 1
        elif code.endswith('.SZ'):
            stats['market_stats']['深市'] += 1

        # 连板统计
        if stock['连板数'] == '首板':
            stats['board_stats']['首板'] += 1
        else:
            stats['board_stats']['连板'] += 1

        # 行业统计
        industries = stock['所属行业'].split(', ') if stock['所属行业'] else []
        for industry in industries:
            if industry and industry != '-':
                stats['industry_stats'][industry] = stats['industry_stats'].get(industry, 0) + 1

    return stats


def export_limit_up_to_excel(limit_up_stocks: List[Dict], filename: str = None) -> str:
    """
    导出涨停股票数据到Excel

    Args:
        limit_up_stocks: 涨停股票列表
        filename: 文件名，默认自动生成

    Returns:
        str: 导出的文件路径
    """
    try:
        if filename is None:
            today = datetime.datetime.now().strftime('%Y%m%d')
            filename = f"limit_up_stocks_{today}.xlsx"

        # 确保文件在data目录下
        if not filename.startswith(DATA_SAVE_DIR):
            daily_dir = ensure_data_dir()
            filepath = os.path.join(daily_dir, filename)
        else:
            filepath = filename

        # 转换为DataFrame
        df = pd.DataFrame(limit_up_stocks)

        # 导出到Excel
        df.to_excel(filepath, index=False, engine='openpyxl')

        print(f">>> 涨停数据已导出到: {filepath}")
        return filepath

    except Exception as e:
        print(f">>> 导出Excel失败: {e}")
        return ""


def export_limit_up_to_csv(limit_up_stocks: List[Dict], filename: str = None) -> str:
    """
    导出涨停股票数据到CSV

    Args:
        limit_up_stocks: 涨停股票列表
        filename: 文件名，默认自动生成

    Returns:
        str: 导出的文件路径
    """
    try:
        if filename is None:
            today = datetime.datetime.now().strftime('%Y%m%d')
            filename = f"limit_up_stocks_{today}.csv"

        # 确保文件在data目录下
        if not filename.startswith(DATA_SAVE_DIR):
            daily_dir = ensure_data_dir()
            filepath = os.path.join(daily_dir, filename)
        else:
            filepath = filename

        # 转换为DataFrame
        df = pd.DataFrame(limit_up_stocks)

        # 导出到CSV
        df.to_csv(filepath, index=False, encoding='utf-8-sig')

        print(f">>> 涨停数据已导出到: {filepath}")
        return filepath

    except Exception as e:
        print(f">>> 导出CSV失败: {e}")
        return ""


def get_historical_limit_up_stocks(start_date: str, end_date: str = None) -> Dict[str, List[Dict]]:
    """
    获取历史涨停股票数据

    Args:
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD，默认为开始日期

    Returns:
        Dict[str, List[Dict]]: 按日期组织的涨停股票数据
    """
    if end_date is None:
        end_date = start_date

    historical_data = {}

    try:
        # 获取日期范围内的交易日
        trading_dates = []
        start_dt = datetime.datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.datetime.strptime(end_date, '%Y%m%d')

        current_dt = start_dt
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y%m%d')
            if is_trading_day(date_str):
                trading_dates.append(date_str)
            current_dt += datetime.timedelta(days=1)

        print(f">>> 获取 {len(trading_dates)} 个交易日的历史涨停数据...")

        for date_str in tqdm(trading_dates, desc="获取历史数据"):
            limit_up_stocks = get_current_limit_up_stocks(date_str)
            if limit_up_stocks:
                historical_data[date_str] = limit_up_stocks

        print(f">>> 成功获取 {len(historical_data)} 天的历史涨停数据")

    except Exception as e:
        print(f">>> 获取历史涨停数据失败: {e}")

    return historical_data


def download_history_data_batch(stock_codes: List[str], start_date: str, end_date: str = None) -> bool:
    """
    批量下载历史数据（供其他文件调用）

    Args:
        stock_codes: 股票代码列表
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD，默认为开始日期

    Returns:
        bool: 是否成功下载
    """
    if end_date is None:
        end_date = start_date

    print(f">>> 批量下载历史数据: {start_date} 到 {end_date}")
    print(f">>> 股票数量: {len(stock_codes)}")

    success_count = 0
    error_count = 0
    batch_size = 50

    try:
        # 优先使用批量下载接口
        if len(stock_codes) > 100:
            try:
                def download_progress_callback(data):
                    if data.get('finished', 0) % 100 == 0:
                        print(f">>> 下载进度: {data.get('finished', 0)}/{data.get('total', 0)}")

                xtdata.download_history_data2(
                    stock_list=stock_codes,
                    period='1d',
                    start_time=start_date,
                    end_time=end_date,
                    callback=download_progress_callback
                )
                print(f">>> 批量下载完成！共 {len(stock_codes)} 只股票")
                return True

            except Exception as e:
                print(f">>> 批量下载失败，改用单只下载: {e}")

        # 单只下载
        for i in tqdm(range(0, len(stock_codes), batch_size), desc="下载历史数据"):
            batch_codes = stock_codes[i:i + batch_size]

            for code in batch_codes:
                try:
                    xtdata.download_history_data(
                        stock_code=code,
                        period='1d',
                        start_time=start_date,
                        end_time=end_date,
                        incrementally=True
                    )
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    if DEBUG_PRINT and error_count <= 5:
                        print(f"[DEBUG] 下载 {code} 失败: {e}")

            # 每批次后稍作休息
            if i < len(stock_codes) - batch_size:
                time.sleep(0.1)

        print(f">>> 历史数据下载完成: 成功 {success_count}, 失败 {error_count}")
        return success_count > error_count

    except Exception as e:
        print(f">>> 批量下载历史数据失败: {e}")
        return False


def create_limit_up_api():
    """
    创建涨停股票API接口（供其他文件调用）

    Returns:
        Dict: API接口字典
    """
    api = {
        # 基础判断函数
        'get_last_trading_day': get_last_trading_day,
        'is_trading_day': is_trading_day,
        'is_trading_time': is_trading_time,
        'is_after_trading_hours': is_after_trading_hours,

        # 数据获取函数
        'get_current_limit_up_stocks': get_current_limit_up_stocks,
        'get_current_limit_up_stocks_realtime': get_current_limit_up_stocks_realtime,
        'get_zhaban_stocks': get_zhaban_stocks,
        'get_limit_down_stocks': get_limit_down_stocks,
        'get_yesterday_limit_up_stocks': get_yesterday_limit_up_stocks,
        'get_realtime_limit_up_stocks': get_realtime_limit_up_stocks,
        'get_historical_limit_up_stocks': get_historical_limit_up_stocks,
        'generate_market_sentiment': generate_market_sentiment,

        # 历史数据下载函数
        'check_and_download_history_data': check_and_download_history_data,
        'download_history_data_batch': download_history_data_batch,
        'check_data_completeness': check_data_completeness,

        # 监控函数
        'monitor_limit_up_stocks': monitor_limit_up_stocks,

        # 统计函数
        'get_limit_up_statistics': get_limit_up_statistics,

        # 导出函数
        'export_limit_up_to_excel': export_limit_up_to_excel,
        'export_limit_up_to_csv': export_limit_up_to_csv,

        # 计算函数
        'calculate_market_value': calculate_market_value,
        'calculate_turnover_rate': calculate_turnover_rate,
        'calculate_seal_amount': calculate_seal_amount,
        'calculate_zhaban_count': calculate_zhaban_count,
        'calculate_consecutive_limit_up': calculate_consecutive_limit_up,
        'get_limit_up_times': get_limit_up_times,

        # 工具函数
        'load_target_stocks': load_target_stocks,
        'download_sector_data_if_needed': download_sector_data_if_needed,
        'ensure_history_data_for_consecutive_calculation': ensure_history_data_for_consecutive_calculation,
        'print_limit_up_summary': print_limit_up_summary,
        'print_limit_up_simple_summary': print_limit_up_simple_summary,
        'print_zhaban_summary': print_zhaban_summary,
        'print_limit_down_summary': print_limit_down_summary,
        'print_yesterday_limit_up_summary': print_yesterday_limit_up_summary,
        'print_market_sentiment': print_market_sentiment,

        # 配置
        'config': config
    }

    return api


if __name__ == "__main__":
    # 测试功能
    print("=== 交易监控模块测试 ===")

    # 测试交易日判断
    today = datetime.datetime.now().strftime('%Y%m%d')
    print(f"今天({today})是否交易日: {is_trading_day()}")

    # 测试交易时间判断
    print(f"当前是否交易时间: {is_trading_time()}")
    print(f"是否收盘后: {is_after_trading_hours()}")

    # 根据时间选择不同的处理方式
    if not is_trading_day():
        print("\n>>> 今天不是交易日")
    elif is_after_trading_hours():
        print("\n>>> 盘后模式：检查并下载历史数据，然后获取当日涨停股票...")

        # 先检查并下载历史数据
        today = datetime.datetime.now().strftime('%Y%m%d')
        if is_trading_day(today):
            print(">>> 今天是交易日，开始检查历史数据...")
            check_and_download_history_data(today)
        else:
            print(">>> 今天不是交易日，获取最近交易日数据...")

        # 获取涨停股票数据
        limit_up_stocks = get_current_limit_up_stocks()
        print_limit_up_summary(limit_up_stocks)

        # 显示统计信息
        stats = get_limit_up_statistics(limit_up_stocks)
        print(f"\n>>> 统计信息:")
        print(f"总计: {stats['total_count']} 只")
        print(f"沪市: {stats['market_stats']['沪市']} 只, 深市: {stats['market_stats']['深市']} 只")
        print(f"首板: {stats['board_stats']['首板']} 只, 连板: {stats['board_stats']['连板']} 只")

    elif is_trading_time():
        print("\n>>> 盘中模式：开始实时监控...")
        # 可以选择单次获取或循环监控
        choice = input("选择模式 (1-单次获取, 2-循环监控): ").strip()
        if choice == '2':
            monitor_limit_up_stocks(interval=30, max_iterations=10)  # 监控10次作为演示
        else:
            limit_up_stocks = get_realtime_limit_up_stocks()
            print_limit_up_summary(limit_up_stocks)
    else:
        print("\n>>> 当前不是交易时间，建议在交易时间或盘后运行")
